'use client';

// import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useDeleteMenu, useUpdateMenu } from '@/hooks/useMenu';
import { MenuListView } from './MenuListView';
import { MenuSkeleton } from './MenuSkeleton';
import { MenuEmptyState } from './MenuEmptyState';
import { Menu } from '@/types';
import { Edit, Trash2, Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';

interface MenuGridProps {
  menus: Menu[];
  isLoading: boolean;
  onEdit: (menu: Menu) => void;
  onAddMenu?: () => void;
  viewMode?: 'grid' | 'list';
  hasFilters?: boolean;
  onClearFilters?: () => void;
}

export function MenuGrid({
  menus,
  isLoading,
  onEdit,
  onAddMenu,
  viewMode = 'grid',
  hasFilters = false,
  onClearFilters,
}: MenuGridProps) {
  const deleteMenu = useDeleteMenu();
  const updateMenu = useUpdateMenu();

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this menu item?')) {
      try {
        await deleteMenu.mutateAsync(id);
        toast.success('Menu item deleted successfully');
      } catch (error) {
        toast.error('Failed to delete menu item');
      }
    }
  };

  const handleToggleAvailability = async (menu: Menu) => {
    try {
      await updateMenu.mutateAsync({
        id: menu.id,
        updates: { available: !menu.available },
      });
      toast.success(`Menu item ${menu.available ? 'disabled' : 'enabled'}`);
    } catch (error) {
      toast.error('Failed to update menu item');
    }
  };

  if (isLoading) {
    return <MenuSkeleton viewMode={viewMode} count={8} />;
  }

  if (menus.length === 0) {
    return (
      <MenuEmptyState
        onAddMenu={onAddMenu || (() => {})}
        hasFilters={hasFilters}
        onClearFilters={onClearFilters}
      />
    );
  }

  // If list view, use the dedicated list component
  if (viewMode === 'list') {
    return <MenuListView menus={menus} onEdit={onEdit} />;
  }

  // Grid view
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {menus.map((menu) => (
        <Card
          key={menu.id}
          className="group hover:shadow-lg transition-all duration-200"
        >
          <div className="relative aspect-video overflow-hidden rounded-t-lg">
            {menu.image_url ? (
              <Image
                src={menu.image_url}
                alt={menu.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-200"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-green-100 to-slate-100 flex items-center justify-center">
                <span className="text-green-600 font-medium">No Image</span>
              </div>
            )}
            <div className="absolute top-2 right-2">
              <Badge variant={menu.available ? 'default' : 'secondary'}>
                {menu.available ? 'Available' : 'Unavailable'}
              </Badge>
            </div>
          </div>

          <CardHeader className="pb-2">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-semibold text-slate-900 line-clamp-1">
                  {menu.name}
                </h3>
                <p className="text-sm text-slate-600 capitalize">
                  {menu.category}
                </p>
              </div>
            </div>
            {menu.description && (
              <p className="text-sm text-slate-600 line-clamp-2">
                {menu.description}
              </p>
            )}
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-lg font-bold text-green-600">
                {new Intl.NumberFormat('id-ID', {
                  style: 'currency',
                  currency: 'IDR',
                }).format(menu.price)}
              </span>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={menu.available}
                  onCheckedChange={() => handleToggleAvailability(menu)}
                  className=" border-2 border-slate-400"
                />
                {menu.available ? (
                  <Eye className="h-4 w-4 text-green-600" />
                ) : (
                  <EyeOff className="h-4 w-4 text-slate-400" />
                )}
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(menu)}
                className="flex-1"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDelete(menu.id)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
