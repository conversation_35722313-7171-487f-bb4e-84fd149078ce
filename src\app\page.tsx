"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/hooks/useUser';
import { Loader2, Pizza, Shield } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function Home() {
  const router = useRouter();
  const { data: user, isLoading, error } = useUser();

  useEffect(() => {
    if (!isLoading && !error) {
      if (user) {
        // Redirect based on user role
        switch (user.role) {
          case 'admin':
            router.push('/dashboard/admin');
            break;
          case 'cashier':
            router.push('/dashboard/cashier');
            break;
          case 'kitchen':
            router.push('/dashboard/kitchen');
            break;
          default:
            router.push('/login');
        }
      } else {
        router.push('/login');
      }
    }
  }, [user, isLoading, error, router]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lupizza-red-50 via-white to-lupizza-cream-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-lupizza-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-lupizza-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Session Error</h2>
            <p className="text-slate-600 mb-4">
              There was an error checking your authentication status.
            </p>
            <Button 
              onClick={() => router.push('/login')}
              className="bg-lupizza-red-600 hover:bg-lupizza-red-700"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-lupizza-green-50 via-white to-lupizza-cream-50 flex items-center justify-center">
      <div className="flex flex-col items-center space-y-6">
        <div className="relative">
          <div className="absolute inset-0 bg-lupizza-gradient rounded-full blur-2xl opacity-20 scale-150"></div>
          <div className="relative bg-lupizza-gradient w-24 h-24 rounded-3xl flex items-center justify-center shadow-lupizza">
            <Pizza className="h-12 w-12 text-white" />
          </div>
        </div>
        
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-lupizza-green-600 to-lupizza-cream-600 bg-clip-text text-transparent">
            LuPizza POS
          </h1>
          <p className="text-slate-600 font-medium">Point of Sale System</p>
        </div>

        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="absolute inset-0 bg-lupizza-gradient rounded-full blur-lg opacity-30 scale-110"></div>
            <div className="relative bg-lupizza-gradient w-16 h-16 rounded-2xl flex items-center justify-center shadow-lupizza">
              <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
          </div>
          <p className="text-lupizza-green-600 font-medium">
            {isLoading ? 'Checking authentication...' : 'Redirecting to dashboard...'}
          </p>
        </div>
      </div>
    </div>
  );
}
