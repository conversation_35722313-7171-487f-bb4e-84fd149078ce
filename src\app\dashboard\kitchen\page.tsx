"use client";

import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { KitchenStats } from '@/components/kitchen/KitchenStats';
import { OrderQueue } from '@/components/kitchen/OrderQueue';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useOrdersByStatus } from '@/hooks/useOrders';
import { Maximize, Volume2 } from 'lucide-react';

export default function KitchenDashboard() {
  const { data: pendingOrders = [] } = useOrdersByStatus('pending');
  const { data: processingOrders = [] } = useOrdersByStatus('processing');
  const { data: readyOrders = [] } = useOrdersByStatus('ready');
  const { data: completedOrders = [] } = useOrdersByStatus('completed');

  const [soundEnabled, setSoundEnabled] = useState(true);
  const [fullscreen, setFullscreen] = useState(false);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setFullscreen(true);
    } else {
      document.exitFullscreen();
      setFullscreen(false);
    }
  };

  // Sound notification for new orders
  useEffect(() => {
    if (soundEnabled && pendingOrders.length > 0) {
      // Play notification sound
      const audio = new Audio('/notification.mp3');
      audio.play().catch(() => {
        // Handle audio play failure silently
      });
    }
  }, [pendingOrders.length, soundEnabled]);

  return (
    <DashboardLayout requiredRole="kitchen">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Kitchen Dashboard
            </h1>
            <p className="text-slate-600 mt-1">Monitor and manage incoming orders</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="sound"
                checked={soundEnabled}
                onCheckedChange={setSoundEnabled}
              />
              <Label htmlFor="sound" className="flex items-center text-sm">
                <Volume2 className="h-4 w-4 mr-1" />
                Sound
              </Label>
            </div>
            <Button variant="outline" onClick={toggleFullscreen}>
              <Maximize className="h-4 w-4 mr-2" />
              {fullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
            </Button>
          </div>
        </div>

        {/* Stats */}
        <KitchenStats 
          pendingOrders={pendingOrders}
          processingOrders={processingOrders}
          readyOrders={readyOrders}
          completedOrders={completedOrders}
        />

        {/* Order Queues */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <OrderQueue
            orders={pendingOrders}
            title="Pending Orders"
            emptyMessage="No pending orders"
            className="lg:col-span-1"
          />
          <OrderQueue
            orders={processingOrders}
            title="In Progress"
            emptyMessage="No orders in progress"
            className="lg:col-span-1"
          />
          <OrderQueue
            orders={readyOrders}
            title="Ready for Pickup"
            emptyMessage="No orders ready"
            className="lg:col-span-1 xl:col-span-1"
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
