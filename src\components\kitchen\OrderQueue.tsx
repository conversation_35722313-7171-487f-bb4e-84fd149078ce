import { OrderCard } from '@/components/ui/OrderCard';
import { OrderWithItems, OrderItemStatus } from '@/types';
import { useUpdateOrderStatus, useUpdateOrderItemStatus } from '@/hooks/useOrders';
import toast from 'react-hot-toast';

interface OrderQueueProps {
  orders: OrderWithItems[];
  title: string;
  emptyMessage?: string;
  className?: string;
}

export function OrderQueue({ 
  orders, 
  title, 
  emptyMessage = "No orders in this queue",
  className 
}: OrderQueueProps) {
  const updateOrderStatus = useUpdateOrderStatus();
  const updateOrderItemStatus = useUpdateOrderItemStatus();

  const handleStatusChange = async (
    orderId: string,
    status: "pending" | "processing" | "ready" | "completed" | "cancelled"
  ) => {
    try {
      await updateOrderStatus.mutateAsync({ id: orderId, status });
      toast.success(`Order ${status}!`);
    } catch (error) {
      toast.error(`Failed to update order status`);
    }
  };

  const handleItemStatusChange = async (itemId: string, status: OrderItemStatus) => {
    try {
      await updateOrderItemStatus.mutateAsync({ id: itemId, status });
      toast.success(`Item ${status}!`);
    } catch (error) {
      toast.error(`Failed to update item status`);
    }
  };

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-slate-900">{title}</h2>
        <span className="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded-full">
          {orders.length} orders
        </span>
      </div>
      
      <div className="space-y-4">
        {orders.length === 0 ? (
          <div className="text-center py-8 text-slate-500">
            <p>{emptyMessage}</p>
          </div>
        ) : (
          orders.map((order) => (
            <OrderCard
              key={order.id}
              order={order}
              onStatusChange={handleStatusChange}
              onItemStatusChange={handleItemStatusChange}
              variant="kitchen"
            />
          ))
        )}
      </div>
    </div>
  );
}