# Admin Dashboard Components

This directory contains all the components used in the admin dashboard of the LuPizza POS system.

## Structure

```
admin/
├── users/              # User management components
│   ├── UserTable.tsx   # Table displaying all users with actions
│   ├── UserForm.tsx    # Form for creating/editing users
│   └── UserFilters.tsx # Filters for user list (role, status)
├── analytics/          # Analytics and reporting components
├── menu/              # Menu management components
├── reports/           # Report generation components
├── settings/          # System settings components
├── AdminStats.tsx     # Dashboard statistics cards
└── RevenueChart.tsx   # Revenue visualization chart
```

## User Management Components

### UserTable.tsx
- Displays paginated list of all users
- Shows user avatar, name, email, role, status, and creation date
- Provides actions: Edit, Activate/Deactivate, Delete
- Handles loading states and empty states
- Uses proper TypeScript types for all props

**Props:**
- `users: Profile[]` - Array of user profiles
- `isLoading: boolean` - Loading state
- `onEdit: (user: Profile) => void` - Callback for edit action

### UserForm.tsx
- Form for creating new users or editing existing ones
- Handles avatar upload with preview
- Role selection with descriptions
- Password field (only for new users)
- Account status toggle
- Form validation using Zod schema

**Props:**
- `user?: Profile | null` - User to edit (null for new user)
- `onSuccess: () => void` - Success callback
- `onCancel: () => void` - Cancel callback

### UserFilters.tsx
- Filter users by role (admin, cashier, kitchen)
- Clear filters functionality
- Visual indicators for active filters

**Props:**
- `selectedRole: string` - Currently selected role filter
- `onRoleChange: (role: string) => void` - Role change callback

## Hooks Used

### useUsers()
- Fetches all users from the database
- Returns `{ data: Profile[], isLoading: boolean, error: any }`
- Automatically refetches when user data changes

### useCreateUser()
- Mutation hook for creating new users
- Handles auth user creation and profile creation
- Supports avatar upload
- Invalidates user queries on success

### useUpdateUser()
- Mutation hook for updating existing users
- Supports partial updates
- Handles avatar upload
- Invalidates user queries on success

### useDeleteUser()
- Mutation hook for deleting users
- Deletes both auth user and profile
- Invalidates user queries on success

### useUpdateUserStatus()
- Mutation hook for activating/deactivating users
- Updates the `is_active` field
- Invalidates user queries on success

## Database Schema

The user management components expect the following Profile schema:

```typescript
interface Profile {
  id: string;
  name: string;           // Legacy field
  full_name?: string;     // Preferred display name
  email: string;
  role: 'admin' | 'cashier' | 'kitchen';
  phone?: string;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

## Key Features

1. **Role-based Access Control**: Only admins can access user management
2. **Real-time Updates**: Uses React Query for automatic data synchronization
3. **Avatar Management**: Upload and manage user profile pictures
4. **Status Management**: Activate/deactivate user accounts
5. **Search & Filter**: Find users by name, email, or role
6. **Form Validation**: Comprehensive validation using Zod
7. **Error Handling**: Proper error states and user feedback
8. **Loading States**: Skeleton loaders and loading indicators
9. **Responsive Design**: Works on all screen sizes
10. **Accessibility**: Proper ARIA labels and keyboard navigation

## Usage Example

```tsx
import { UserTable } from '@/components/admin/users/UserTable';
import { useUsers } from '@/hooks/useUsers';

function UserManagementPage() {
  const { data: users = [], isLoading } = useUsers();
  
  const handleEdit = (user: Profile) => {
    // Handle edit logic
  };

  return (
    <UserTable 
      users={users}
      isLoading={isLoading}
      onEdit={handleEdit}
    />
  );
}
```

## Error Handling

All components include proper error handling:
- Network errors are caught and displayed to users
- Form validation errors are shown inline
- Loading states prevent user confusion
- Success/error toasts provide feedback

## Performance Considerations

- Uses React Query for efficient data fetching and caching
- Implements debounced search to reduce API calls
- Lazy loading for large user lists
- Optimistic updates for better UX
- Proper memoization where needed

## Security

- All user operations require admin role
- Row Level Security (RLS) policies in database
- Input sanitization and validation
- Secure file upload for avatars
- Proper authentication checks
