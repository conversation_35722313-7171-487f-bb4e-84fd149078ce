import { supabase } from '@/lib/supabase';
import { Order, OrderItem, OrderWithItems, OrderInsert, OrderItemInsert, Transaction, TransactionInsert, OrderStatus } from '@/types';

export const orderService = {
  async getOrders(filters?: {
    status?: string;
    orderType?: string;
    branchId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<OrderWithItems[]> {
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          menu_items (
            id,
            name,
            base_price,
            is_beverage,
            categories (
              id,
              name
            )
          )
        ),
        users (
          id,
          full_name,
          role
        ),
        branches (
          id,
          name,
          address
        )
      `);

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.orderType) {
      query = query.eq('order_type', filters.orderType);
    }
    if (filters?.branchId) {
      query = query.eq('branch_id', filters.branchId);
    }
    if (filters?.startDate) {
      query = query.gte('order_date', filters.startDate);
    }
    if (filters?.endDate) {
      query = query.lte('order_date', filters.endDate);
    }

    const { data, error } = await query
      .order('order_date', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async getOrderById(id: string): Promise<OrderWithItems | null> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          menu_items (
            id,
            name,
            base_price,
            is_beverage,
            categories (
              id,
              name
            )
          )
        ),
        users (
          id,
          full_name,
          role
        ),
        branches (
          id,
          name,
          address
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  },

  async updateOrderStatus(id: string, status: OrderStatus): Promise<Order> {
    const { data, error } = await supabase
      .from('orders')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async updateOrderItemPrepared(id: string, isPrepared: boolean): Promise<OrderItem> {
    const { data, error } = await supabase
      .from('order_items')
      .update({
        is_prepared: isPrepared,
        prepared_at: isPrepared ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async createOrder(orderData: {
    order_number: string;
    order_type: 'dine_in' | 'take_away' | 'delivery_grab' | 'delivery_gojek';
    table_number?: string;
    customer_name?: string;
    customer_phone?: string;
    subtotal: number;
    discount_amount?: number;
    tax_amount?: number;
    total_amount: number;
    notes?: string;
    cashier_id: string;
    branch_id: string;
    items: Array<{
      menu_item_id: string;
      quantity: number;
      unit_price: number;
      total_price: number;
      notes?: string;
      variant_selections?: any;
    }>;
  }): Promise<OrderWithItems> {
    try {
      // Start transaction
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          order_number: orderData.order_number,
          order_type: orderData.order_type,
          table_number: orderData.table_number,
          customer_name: orderData.customer_name,
          customer_phone: orderData.customer_phone,
          subtotal: orderData.subtotal,
          discount_amount: orderData.discount_amount || 0,
          tax_amount: orderData.tax_amount || 0,
          total_amount: orderData.total_amount,
          notes: orderData.notes,
          cashier_id: orderData.cashier_id,
          branch_id: orderData.branch_id,
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Insert order items
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        menu_item_id: item.menu_item_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        notes: item.notes,
        variant_selections: item.variant_selections,
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Return the complete order
      return await this.getOrderById(order.id) as OrderWithItems;
    } catch (error) {
      console.error('Create order error:', error);
      throw error;
    }
  },

  async createTransaction(transactionData: {
    order_id: string;
    payment_method: 'cash' | 'qris' | 'card' | 'split';
    amount_paid: number;
    change_amount?: number;
    payment_details?: any;
  }): Promise<Transaction> {
    const { data, error } = await supabase
      .from('transactions')
      .insert({
        ...transactionData,
        change_amount: transactionData.change_amount || 0,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async generateOrderNumber(): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');

    // Get today's order count
    const { count, error } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .gte('order_date', `${dateStr}T00:00:00.000Z`)
      .lt('order_date', `${dateStr}T23:59:59.999Z`);

    if (error) throw error;

    const orderNumber = `ORD-${dateStr}-${String((count || 0) + 1).padStart(4, '0')}`;
    return orderNumber;
  },

  // Kitchen specific methods
  async getKitchenOrders(branchId?: string): Promise<OrderWithItems[]> {
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          menu_items (
            id,
            name,
            base_price,
            is_beverage,
            categories (
              id,
              name
            )
          )
        ),
        users (
          id,
          full_name
        )
      `)
      .in('status', ['pending', 'processing']);

    if (branchId) {
      query = query.eq('branch_id', branchId);
    }

    const { data, error } = await query
      .order('order_date', { ascending: true });

    if (error) throw error;
    return data || [];
  },
};
