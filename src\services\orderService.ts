import { supabase } from '@/lib/supabase';
import { Order, OrderWithItems, CartItem, Transaction, OrderStatus, OrderItemStatus, OrderItem } from '@/types';

export const orderService = {
  async updateOrderStatus(id: string, status: OrderStatus): Promise<Order> {
    const { data, error } = await supabase
      .from('orders')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateOrderItemStatus(id: string, status: OrderItemStatus): Promise<OrderItem> {
    const { data, error } = await supabase
      .from('order_items')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async createOrder(order: Omit<Order, 'id' | 'created_at' | 'updated_at'>): Promise<Order> {
    const { data, error } = await supabase
      .from('orders')
      .insert(order)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async createOrderItems(orderItems: Omit<OrderItem, 'id' | 'created_at' | 'updated_at'>[]): Promise<OrderItem[]> {
    const { data, error } = await supabase
      .from('order_items')
      .insert(orderItems)
      .select();
    
    if (error) throw error;
    return data || [];
  },

  async createTransaction(transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>): Promise<Transaction> {
    const { data, error } = await supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async createCompleteOrder(
    orderData: Omit<Order, 'id' | 'created_at' | 'updated_at'>,
    cartItems: CartItem[],
    transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'order_id'>
  ): Promise<OrderWithItems> {
    // Create order
    const order = await this.createOrder(orderData);
    
    // Create order items
    const orderItems = await this.createOrderItems(
      cartItems.map(item => ({
        order_id: order.id,
        menu_id: item.menu_id,
        quantity: item.quantity,
        notes: item.notes || '',
        status: 'pending', 
      }))
    );
    
    // Create transaction
    const transaction = await this.createTransaction({
      ...transactionData,
      order_id: order.id,
    });
    
    // Return complete order with items and transaction
    return {
      ...order,
      order_items: orderItems.map(item => ({
        ...item,
        menu: cartItems.find(cartItem => cartItem.menu_id === item.menu_id)?.menu!,
      })),
      transaction,
    };
  },

  async getOrders(): Promise<OrderWithItems[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          menu:menus (*)
        ),
        transaction:transactions (*)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getOrdersByStatus(status: Order['status']): Promise<OrderWithItems[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          menu:menus (*)
        ),
        transaction:transactions (*)
      `)
      .eq('status', status)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async subscribeToOrders(callback: (payload: any) => void) {
    return supabase
      .channel('orders')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'orders' },
        callback
      )
      .subscribe();
  },

  async subscribeToOrderItems(callback: (payload: any) => void) {
    return supabase
      .channel('order_items')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'order_items' },
        callback
      )
      .subscribe();
  },
};
