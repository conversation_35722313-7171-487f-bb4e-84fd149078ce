import { storageService } from '@/services/storageService';

/**
 * Initialize storage bucket for menu images
 * This should be called once when the application starts
 */
export async function initializeStorage() {
  try {
    await storageService.initializeBucket();
    console.log('Storage bucket initialized successfully');
  } catch (error) {
    console.error('Failed to initialize storage bucket:', error);
    // Don't throw error to prevent app from crashing
    // Storage will be created on first upload attempt
  }
}

// Auto-initialize storage when this module is imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  initializeStorage();
}
