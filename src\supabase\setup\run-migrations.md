# Panduan Menjalankan Database Migrations

## Langkah-langkah:

### 1. Buka Supabase Dashboard
- Buka: https://supabase.com/dashboard/project/ajujvjdvyqkhawuajvxn
- Login dengan akun Supabase Anda

### 2. <PERSON><PERSON><PERSON> ke SQL Editor
- Di sidebar kiri, k<PERSON> "SQL Editor"
- <PERSON><PERSON> "New query" untuk membuat query baru

### 3. Jalankan Migrations Secara Berurutan

#### Migration 1: Initial Schema
1. Copy seluruh isi file `src/supabase/migration/001_initial_schema.sql`
2. Paste ke SQL Editor
3. <PERSON><PERSON> "Run" untuk mengeksekusi
4. Pastikan tidak ada error

#### Migration 2: Sample Data
1. Copy seluruh isi file `src/supabase/migration/002_sample_data.sql`
2. Paste ke SQL Editor (query baru)
3. Klik "Run" untuk mengeksekusi
4. Pastikan tidak ada error

#### Migration 3: RLS Policies
1. Copy seluruh isi file `src/supabase/migration/003_rls_policies.sql`
2. Paste ke SQL Editor (query baru)
3. <PERSON><PERSON> "Run" untuk mengeksekusi
4. Pastikan tidak ada error

### 4. Verifikasi Database
Setelah semua migration berhasil, verifikasi dengan:

1. **Cek Tables**: Di sidebar kiri, klik "Table Editor" dan pastikan semua tabel sudah terbuat:
   - branches
   - users
   - categories
   - menu_items
   - item_variants
   - promotions
   - orders
   - order_items
   - transactions
   - activity_logs

2. **Cek Sample Data**: Klik pada tabel `categories` dan `menu_items` untuk memastikan sample data sudah masuk

3. **Cek RLS**: Di "Table Editor", klik pada salah satu tabel dan pastikan "Enable RLS" sudah aktif

### 5. Setup Authentication
1. Di sidebar kiri, klik "Authentication"
2. Klik "Users" tab
3. Klik "Add user" untuk membuat user admin pertama:
   - Email: <EMAIL>
   - Password: (buat password yang kuat)
   - Auto Confirm User: ✅

### 6. Insert User Data
Setelah user admin dibuat, jalankan query berikut di SQL Editor untuk menambahkan data user:

```sql
-- Ganti 'USER_UUID_DARI_AUTH' dengan UUID user yang baru dibuat
INSERT INTO users (id, email, full_name, role, branch_id) VALUES 
('USER_UUID_DARI_AUTH', '<EMAIL>', 'Admin Lupizza', 'admin', '550e8400-e29b-41d4-a716-************');
```

### 7. Test Connection
Setelah semua selesai, test koneksi dari aplikasi Next.js dengan menjalankan:
```bash
npm run dev
```

## Troubleshooting

### Jika ada error "relation does not exist":
- Pastikan migration 001 sudah dijalankan dengan sukses
- Cek di Table Editor apakah tabel sudah terbuat

### Jika ada error RLS:
- Pastikan semua migration sudah dijalankan berurutan
- Cek di Authentication apakah user sudah dibuat

### Jika ada error foreign key:
- Pastikan sample data dijalankan setelah schema
- Cek apakah UUID yang direferensikan sudah ada

## Setelah Setup Selesai
Lanjut ke Task 1.4: Update TypeScript Types
