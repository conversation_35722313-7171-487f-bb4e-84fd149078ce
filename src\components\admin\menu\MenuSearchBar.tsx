'use client';

import { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
import { MenuItem } from '@/types';

interface MenuSearchBarProps {
  value: string;
  onChange: (value: string) => void;
  menus: MenuItem[];
  placeholder?: string;
}

export function MenuSearchBar({
  value,
  onChange,
  menus,
  placeholder = 'Search menu items...',
}: MenuSearchBarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Get search suggestions based on menu items
  const suggestions =
    value.length > 0
      ? menus
          .filter(
            (menu) =>
              menu.name.toLowerCase().includes(value.toLowerCase()) ||
              menu.categories?.name
                ?.toLowerCase()
                .includes(value.toLowerCase()) ||
              menu.description?.toLowerCase().includes(value.toLowerCase())
          )
          .slice(0, 5)
          .map((menu) => ({
            type: 'menu' as const,
            text: menu.name,
            category: menu.categories?.name || 'No Category',
            id: menu.id,
          }))
      : [];

  // Get popular categories
  const popularCategories = [
    ...new Set(menus.map((menu) => menu.categories?.name).filter(Boolean)),
  ]
    .slice(0, 4)
    .map((category) => ({
      type: 'category' as const,
      text: category,
      count: menus.filter((menu) => menu.categories?.name === category).length,
    }));

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (newValue: string) => {
    onChange(newValue);
    setIsOpen(newValue.length > 0 || true);
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setIsOpen(false);

    // Add to recent searches
    setRecentSearches((prev) => {
      const filtered = prev.filter((item) => item !== suggestion);
      return [suggestion, ...filtered].slice(0, 5);
    });
  };

  const handleClear = () => {
    onChange('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const handleFocus = () => {
    setIsOpen(true);
  };

  return (
    <div ref={containerRef} className="relative flex-1">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
        <Input
          ref={inputRef}
          placeholder={placeholder}
          value={value}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleFocus}
          className="pl-10 pr-10 bg-white border-slate-200"
        />
        {value && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Search Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-slate-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
          {/* Current search suggestions */}
          {suggestions.length > 0 && (
            <div className="p-2">
              <div className="text-xs font-medium text-slate-500 mb-2 px-2">
                Menu Items
              </div>
              {suggestions.map((suggestion) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion.text)}
                  className="w-full text-left px-3 py-2 hover:bg-slate-50 rounded-md flex items-center justify-between group"
                >
                  <div>
                    <div className="font-medium text-slate-900">
                      {suggestion.text}
                    </div>
                    <div className="text-xs text-slate-500 capitalize">
                      {suggestion.category}
                    </div>
                  </div>
                  <Search className="h-3 w-3 text-slate-400 opacity-0 group-hover:opacity-100" />
                </button>
              ))}
            </div>
          )}

          {/* Popular categories when no search */}
          {value.length === 0 && popularCategories.length > 0 && (
            <div className="p-2">
              <div className="text-xs font-medium text-slate-500 mb-2 px-2 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                Popular Categories
              </div>
              {popularCategories.map((category) => (
                <button
                  key={category.text}
                  onClick={() => handleSuggestionClick(category.text)}
                  className="w-full text-left px-3 py-2 hover:bg-slate-50 rounded-md flex items-center justify-between group"
                >
                  <div className="flex items-center">
                    <span className="font-medium text-slate-900 capitalize">
                      {category.text}
                    </span>
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {category.count}
                    </Badge>
                  </div>
                  <Search className="h-3 w-3 text-slate-400 opacity-0 group-hover:opacity-100" />
                </button>
              ))}
            </div>
          )}

          {/* Recent searches */}
          {value.length === 0 && recentSearches.length > 0 && (
            <div className="p-2 border-t border-slate-100">
              <div className="text-xs font-medium text-slate-500 mb-2 px-2 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                Recent Searches
              </div>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(search)}
                  className="w-full text-left px-3 py-2 hover:bg-slate-50 rounded-md flex items-center justify-between group"
                >
                  <span className="text-slate-700">{search}</span>
                  <Search className="h-3 w-3 text-slate-400 opacity-0 group-hover:opacity-100" />
                </button>
              ))}
            </div>
          )}

          {/* No results */}
          {value.length > 0 && suggestions.length === 0 && (
            <div className="p-4 text-center text-slate-500">
              <Search className="h-8 w-8 mx-auto mb-2 text-slate-300" />
              <p className="text-sm">No menu items found for "{value}"</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
