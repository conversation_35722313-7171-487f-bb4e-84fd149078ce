import { supabase } from '@/lib/supabase';
import { User, UserInsert } from '@/types';

export const userService = {
  async getUsers(): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        branches (
          id,
          name,
          address
        )
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        branches (
          id,
          name,
          address
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  },

  async createUser(userData: {
    full_name: string;
    email: string;
    password: string;
    role: 'admin' | 'cashier' | 'kitchen';
    branch_id?: string;
    is_active?: boolean;
  }): Promise<User> {
    try {
      // First create the auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
        user_metadata: {
          full_name: userData.full_name,
          role: userData.role,
        }
      });

      if (authError) throw authError;

      // Avatar upload removed for now - can be added later if needed

      // Then create the user profile
      const { data, error } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          role: userData.role,
          branch_id: userData.branch_id,
          is_active: userData.is_active ?? true,
        })
        .select(`
          *,
          branches (
            id,
            name,
            address
          )
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  },

  async updateUser(id: string, updates: {
    full_name?: string;
    role?: 'admin' | 'cashier' | 'kitchen';
    branch_id?: string;
    is_active?: boolean;
  }): Promise<User> {
    try {
      const updateData: any = {
        updated_at: new Date().toISOString(),
      };

      if (updates.full_name !== undefined) updateData.full_name = updates.full_name;
      if (updates.role !== undefined) updateData.role = updates.role;
      if (updates.branch_id !== undefined) updateData.branch_id = updates.branch_id;
      if (updates.is_active !== undefined) updateData.is_active = updates.is_active;

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          branches (
            id,
            name,
            address
          )
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  },

  async deleteUser(id: string): Promise<void> {
    try {
      // Delete the auth user (this will cascade to users table due to foreign key)
      const { error: authError } = await supabase.auth.admin.deleteUser(id);
      if (authError) throw authError;

      // Also delete from users table as backup
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);

      if (error && error.code !== 'PGRST116') { // Ignore "not found" errors
        throw error;
      }
    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  },

  async updateUserStatus(id: string, status: 'active' | 'inactive'): Promise<User> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          is_active: status === 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select(`
          *,
          branches (
            id,
            name,
            address
          )
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update user status error:', error);
      throw error;
    }
  },

  async searchUsers(query: string, role?: string): Promise<User[]> {
    let queryBuilder = supabase
      .from('users')
      .select(`
        *,
        branches (
          id,
          name,
          address
        )
      `);

    if (query) {
      queryBuilder = queryBuilder.or(
        `full_name.ilike.%${query}%,email.ilike.%${query}%`
      );
    }

    if (role && role !== 'all') {
      queryBuilder = queryBuilder.eq('role', role);
    }

    const { data, error } = await queryBuilder
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<string, number>;
  }> {
    const { data, error } = await supabase
      .from('users')
      .select('role, is_active');

    if (error) throw error;

    const stats = {
      total: data.length,
      active: data.filter(u => u.is_active !== false).length,
      inactive: data.filter(u => u.is_active === false).length,
      byRole: data.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return stats;
  }
};
