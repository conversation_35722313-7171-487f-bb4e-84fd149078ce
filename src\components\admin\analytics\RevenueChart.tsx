"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format, eachDayOfInterval, startOfDay } from 'date-fns';
import { TrendingUp } from 'lucide-react';

interface RevenueChartProps {
  orders: any[];
  dateRange: { from: Date; to: Date };
}

export function RevenueChart({ orders, dateRange }: RevenueChartProps) {
  const chartData = eachDayOfInterval({
    start: startOfDay(dateRange.from),
    end: startOfDay(dateRange.to)
  }).map(date => {
    const dayOrders = orders.filter(order => 
      startOfDay(new Date(order.created_at)).getTime() === date.getTime()
    );
    
    const revenue = dayOrders.reduce((sum, order) => sum + order.total, 0);
    
    return {
      date: format(date, 'MMM dd'),
      revenue,
      orders: dayOrders.length
    };
  });

  const totalRevenue = chartData.reduce((sum, day) => sum + day.revenue, 0);
  const avgDaily = chartData.length > 0 ? totalRevenue / chartData.length : 0;

  return (
    <Card className="col-span-1">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">Daily Revenue</CardTitle>
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-4 w-4 text-lupizza-green-600" />
          <span className="text-sm text-slate-600">
            Avg: {new Intl.NumberFormat('id-ID', { 
              style: 'currency', 
              currency: 'IDR' 
            }).format(avgDaily)}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-slate-200" />
              <XAxis 
                dataKey="date" 
                className="text-xs fill-slate-600"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs fill-slate-600"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip 
                formatter={(value: number) => [
                  new Intl.NumberFormat('id-ID', { 
                    style: 'currency', 
                    currency: 'IDR' 
                  }).format(value),
                  'Revenue'
                ]}
                labelStyle={{ color: '#334155' }}
                contentStyle={{ 
                  backgroundColor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#22c55e" 
                strokeWidth={3}
                dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#22c55e', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}