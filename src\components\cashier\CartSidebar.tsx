import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CartItem } from '@/types';
import { Plus, Minus, Trash2, ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CartSidebarProps {
  cart: CartItem[];
  onUpdateQuantity: (menuId: string, quantity: number) => void;
  onRemoveItem: (menuId: string) => void;
  onClearCart: () => void;
  className?: string;
}

export function CartSidebar({ 
  cart, 
  onUpdateQuantity, 
  onRemoveItem, 
  onClearCart,
  className 
}: CartSidebarProps) {
  const subtotal = cart.reduce((sum, item) => sum + (item.menu.price * item.quantity), 0);
  const tax = subtotal * 0.1; // 10% tax
  const total = subtotal + tax;

  return (
    <Card className={cn("h-fit", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5" />
            <span>Cart ({cart.length})</span>
          </CardTitle>
          {cart.length > 0 && (
            <Button variant="outline" size="sm" onClick={onClearCart}>
              <Trash2 className="h-4 w-4 mr-2" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {cart.map(item => (
            <div key={item.menu_id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
              <div className="flex-1 min-w-0">
                <p className="font-medium text-slate-900 truncate">{item.menu.name}</p>
                <p className="text-sm text-slate-600">
                  Rp {item.menu.price.toLocaleString('id-ID')} each
                </p>
                <p className="text-xs text-slate-500">
                  Total: Rp {(item.menu.price * item.quantity).toLocaleString('id-ID')}
                </p>
              </div>
              <div className="flex items-center space-x-2 ml-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onUpdateQuantity(item.menu_id, item.quantity - 1)}
                  className="h-8 w-8 p-0"
                >
                  <Minus className="h-3 w-3" />
                </Button>
                <span className="w-8 text-center text-sm font-medium">{item.quantity}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onUpdateQuantity(item.menu_id, item.quantity + 1)}
                  className="h-8 w-8 p-0"
                >
                  <Plus className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemoveItem(item.menu_id)}
                  className="h-8 w-8 p-0 text-lupizza-red-600 hover:text-lupizza-red-700 hover:bg-lupizza-red-50"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
          {cart.length === 0 && (
            <div className="text-center py-8 text-slate-500">
              <ShoppingCart className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Your cart is empty</p>
              <p className="text-xs">Add items from the menu</p>
            </div>
          )}
        </div>

        {/* Cart Summary */}
        {cart.length > 0 && (
          <div className="border-t pt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-600">Subtotal:</span>
              <span className="font-medium">Rp {subtotal.toLocaleString('id-ID')}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-slate-600">Tax (10%):</span>
              <span className="font-medium">Rp {tax.toLocaleString('id-ID')}</span>
            </div>
            <div className="flex justify-between text-lg font-bold border-t pt-2">
              <span>Total:</span>
              <span className="text-lupizza-green-600">Rp {total.toLocaleString('id-ID')}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}