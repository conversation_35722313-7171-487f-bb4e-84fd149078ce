"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface ModernChartProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  className?: string;
  headerAction?: ReactNode;
  loading?: boolean;
  isEmpty?: boolean;
  emptyMessage?: string;
}

export function ModernChart({
  title,
  subtitle,
  children,
  className,
  headerAction,
  loading = false,
  isEmpty = false,
  emptyMessage = "No data available"
}: ModernChartProps) {
  return (
    <Card className={cn(
      "overflow-hidden border-0 shadow-sm bg-white/50 backdrop-blur-sm",
      "hover:shadow-md transition-all duration-200",
      className
    )}>
      <CardHeader className="pb-4 bg-gradient-to-r from-white/80 to-slate-50/50 border-b border-slate-100/50">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold text-slate-900 tracking-tight">
              {title}
            </CardTitle>
            {subtitle && (
              <p className="text-sm text-slate-600 font-medium">
                {subtitle}
              </p>
            )}
          </div>
          {headerAction && (
            <div className="flex items-center space-x-2">
              {headerAction}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="space-y-3 text-center">
              <div className="w-8 h-8 border-2 border-green-200 border-t-green-600 rounded-full animate-spin mx-auto"></div>
              <p className="text-sm text-slate-500">Loading chart data...</p>
            </div>
          </div>
        ) : isEmpty ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-1">No Data</h3>
                <p className="text-sm text-slate-500">{emptyMessage}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="relative">
            {children}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
