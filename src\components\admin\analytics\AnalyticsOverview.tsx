"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { StatCard } from '@/components/ui/StatCard';
import { TrendingUp, DollarSign, ShoppingCart, Users, Target } from 'lucide-react';

interface AnalyticsOverviewProps {
  analytics?: {
    totalRevenue: number;
    totalOrders: number;
    avgOrderValue: number;
    orders: any[];
  };
}

export function AnalyticsOverview({ analytics }: AnalyticsOverviewProps) {
  if (!analytics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-slate-200 rounded w-24"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-slate-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-slate-200 rounded w-20"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const stats = [
    {
      title: 'Total Revenue',
      value: analytics.totalRevenue,
      format: 'currency',
      icon: DollarSign,
      trend: 'up',
      change: 12.5
    },
    {
      title: 'Total Orders',
      value: analytics.totalOrders,
      format: 'number',
      icon: ShoppingCart,
      trend: 'up',
      change: 8.2
    },
    {
      title: 'Average Order Value',
      value: analytics.avgOrderValue,
      format: 'currency',
      icon: Target,
      trend: 'up',
      change: 5.1
    },
    {
      title: 'Growth Rate',
      value: 15.8,
      format: 'percentage',
      icon: TrendingUp,
      trend: 'up',
      change: 2.3
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          format={stat.format as any}
          icon={stat.icon}
          trend={stat.trend as any}
          change={stat.change}
        />
      ))}
    </div>
  );
}