"use client";

import { <PERSON><PERSON><PERSON> } from '@/components/ui/ModernChart';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { Order } from '@/types';
import { useMemo } from 'react';

interface OrderStatusChartProps {
  orders: Order[];
  className?: string;
}

const STATUS_COLORS = {
  completed: '#22c55e',
  processing: '#3b82f6', 
  pending: '#eab308',
  cancelled: '#ef4444'
};

const STATUS_LABELS = {
  completed: 'Completed',
  processing: 'Processing',
  pending: 'Pending',
  cancelled: 'Cancelled'
};

export function OrderStatusChart({ orders, className }: OrderStatusChartProps) {
  const chartData = useMemo(() => {
    // Get status counts
    const statusCounts = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Convert to chart data and sort by count (descending)
    const data = Object.entries(statusCounts)
      .map(([status, count]) => ({
        name: STATUS_LABELS[status as keyof typeof STATUS_LABELS] || status,
        value: count,
        status: status,
        color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || '#6b7280',
        percentage: orders.length > 0 ? ((count / orders.length) * 100).toFixed(1) : '0'
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 3); // Show only top 3

    return data;
  }, [orders]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-slate-200 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: data.color }}
            />
            <span className="font-medium text-slate-900">{data.name}</span>
          </div>
          <div className="mt-1 text-sm text-slate-600">
            <div>Orders: {data.value}</div>
            <div>Percentage: {data.percentage}%</div>
          </div>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-col space-y-2 mt-4">
        {payload?.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-slate-700 font-medium">{entry.value}</span>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-slate-900 font-semibold">
                {entry.payload.value}
              </span>
              <span className="text-slate-500 text-xs">
                {entry.payload.percentage}%
              </span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const isEmpty = chartData.length === 0;
  const totalOrders = orders.length;

  return (
    <ModernChart
      title="Order Status"
      subtitle={`Top 3 status from ${totalOrders} orders`}
      className={className}
      isEmpty={isEmpty}
      emptyMessage="No orders to display"
    >
      {!isEmpty && (
        <div className="space-y-4">
          <ResponsiveContainer width="100%" height={240}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
                stroke="none"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
          
          {/* Custom Legend */}
          <CustomLegend payload={chartData.map(item => ({
            value: item.name,
            color: item.color,
            payload: item
          }))} />
        </div>
      )}
    </ModernChart>
  );
}
