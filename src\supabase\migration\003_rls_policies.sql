-- =============================================
-- Row Level Security (RLS) Policies
-- =============================================

-- Enable RLS on all tables
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;

ALTER TABLE users ENABLE ROW LEVEL SECURITY;

ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;

ALTER TABLE item_variants ENABLE ROW LEVEL SECURITY;

ALTER TABLE promotions ENABLE ROW LEVEL SECURITY;

ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT role 
    FROM users 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's branch
CREATE OR REPLACE FUNCTION get_user_branch()
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT branch_id 
    FROM users 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- BRANCHES POLICIES
-- =============================================

-- Admin can do everything with branches
CREATE POLICY "Admin can manage branches" ON branches FOR ALL USING (get_user_role () = 'admin');

-- Cashier and kitchen can only read their own branch
CREATE POLICY "Users can read own branch" ON branches FOR
SELECT USING (
        get_user_role () IN ('cashier', 'kitchen')
        AND id = get_user_branch ()
    );

-- =============================================
-- USERS POLICIES
-- =============================================

-- Admin can manage all users
CREATE POLICY "Admin can manage users" ON users FOR ALL USING (get_user_role () = 'admin');

-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON users FOR
SELECT USING (id = auth.uid ());

-- Users can update their own profile (limited fields)
CREATE POLICY "Users can update own profile" ON users FOR
UPDATE USING (id = auth.uid ())
WITH
    CHECK (id = auth.uid ());

-- =============================================
-- CATEGORIES POLICIES
-- =============================================

-- Admin can manage categories
CREATE POLICY "Admin can manage categories" ON categories FOR ALL USING (get_user_role () = 'admin');

-- Cashier and kitchen can read categories
CREATE POLICY "Staff can read categories" ON categories FOR
SELECT USING (
        get_user_role () IN ('cashier', 'kitchen')
    );

-- =============================================
-- MENU ITEMS POLICIES
-- =============================================

-- Admin can manage menu items
CREATE POLICY "Admin can manage menu items" ON menu_items FOR ALL USING (get_user_role () = 'admin');

-- Cashier and kitchen can read menu items
CREATE POLICY "Staff can read menu items" ON menu_items FOR
SELECT USING (
        get_user_role () IN ('cashier', 'kitchen')
    );

-- =============================================
-- ITEM VARIANTS POLICIES
-- =============================================

-- Admin can manage item variants
CREATE POLICY "Admin can manage item variants" ON item_variants FOR ALL USING (get_user_role () = 'admin');

-- Cashier and kitchen can read item variants
CREATE POLICY "Staff can read item variants" ON item_variants FOR
SELECT USING (
        get_user_role () IN ('cashier', 'kitchen')
    );

-- =============================================
-- PROMOTIONS POLICIES
-- =============================================

-- Admin can manage promotions
CREATE POLICY "Admin can manage promotions" ON promotions FOR ALL USING (get_user_role () = 'admin');

-- Cashier can read active promotions
CREATE POLICY "Cashier can read promotions" ON promotions FOR
SELECT USING (
        get_user_role () = 'cashier'
        AND is_active = true
        AND start_date <= NOW()
        AND end_date >= NOW()
    );

-- =============================================
-- ORDERS POLICIES
-- =============================================

-- Admin can see all orders
CREATE POLICY "Admin can manage orders" ON orders FOR ALL USING (get_user_role () = 'admin');

-- Cashier can manage orders in their branch
CREATE POLICY "Cashier can manage orders" ON orders FOR ALL USING (
    get_user_role () = 'cashier'
    AND branch_id = get_user_branch ()
);

-- Kitchen can read orders in their branch
CREATE POLICY "Kitchen can read orders" ON orders FOR
SELECT USING (
        get_user_role () = 'kitchen'
        AND branch_id = get_user_branch ()
    );

-- Kitchen can update order status
CREATE POLICY "Kitchen can update order status" ON orders FOR
UPDATE USING (
    get_user_role () = 'kitchen'
    AND branch_id = get_user_branch ()
)
WITH
    CHECK (
        get_user_role () = 'kitchen'
        AND branch_id = get_user_branch ()
    );

-- =============================================
-- ORDER ITEMS POLICIES
-- =============================================

-- Admin can see all order items
CREATE POLICY "Admin can manage order items" ON order_items FOR ALL USING (get_user_role () = 'admin');

-- Cashier can manage order items for orders in their branch
CREATE POLICY "Cashier can manage order items" ON order_items FOR ALL USING (
    get_user_role () = 'cashier'
    AND EXISTS (
        SELECT 1
        FROM orders
        WHERE
            orders.id = order_items.order_id
            AND orders.branch_id = get_user_branch ()
    )
);

-- Kitchen can read and update order items for orders in their branch
CREATE POLICY "Kitchen can read order items" ON order_items FOR
SELECT USING (
        get_user_role () = 'kitchen'
        AND EXISTS (
            SELECT 1
            FROM orders
            WHERE
                orders.id = order_items.order_id
                AND orders.branch_id = get_user_branch ()
        )
    );

CREATE POLICY "Kitchen can update order items" ON order_items FOR
UPDATE USING (
    get_user_role () = 'kitchen'
    AND EXISTS (
        SELECT 1
        FROM orders
        WHERE
            orders.id = order_items.order_id
            AND orders.branch_id = get_user_branch ()
    )
)
WITH
    CHECK (
        get_user_role () = 'kitchen'
        AND EXISTS (
            SELECT 1
            FROM orders
            WHERE
                orders.id = order_items.order_id
                AND orders.branch_id = get_user_branch ()
        )
    );

-- =============================================
-- TRANSACTIONS POLICIES
-- =============================================

-- Admin can see all transactions
CREATE POLICY "Admin can manage transactions" ON transactions FOR ALL USING (get_user_role () = 'admin');

-- Cashier can manage transactions for orders in their branch
CREATE POLICY "Cashier can manage transactions" ON transactions FOR ALL USING (
    get_user_role () = 'cashier'
    AND EXISTS (
        SELECT 1
        FROM orders
        WHERE
            orders.id = transactions.order_id
            AND orders.branch_id = get_user_branch ()
    )
);

-- =============================================
-- ACTIVITY LOGS POLICIES
-- =============================================

-- Admin can see all activity logs
CREATE POLICY "Admin can read activity logs" ON activity_logs FOR
SELECT USING (get_user_role () = 'admin');

-- Users can insert their own activity logs
CREATE POLICY "Users can insert activity logs" ON activity_logs FOR
INSERT
WITH
    CHECK (user_id = auth.uid ());

-- Users can read their own activity logs
CREATE POLICY "Users can read own activity logs" ON activity_logs FOR
SELECT USING (user_id = auth.uid ());

-- =============================================
-- ENABLE REALTIME
-- =============================================

-- Enable realtime for orders and order_items (for kitchen dashboard)
ALTER PUBLICATION supabase_realtime ADD TABLE orders;

ALTER PUBLICATION supabase_realtime ADD TABLE order_items;