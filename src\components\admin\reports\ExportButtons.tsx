"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, FileText, FileSpreadsheet, File } from 'lucide-react';

interface ExportButtonsProps {
  onExport: () => void;
  isExporting: boolean;
  format: 'pdf' | 'excel' | 'csv';
  onFormatChange: (format: 'pdf' | 'excel' | 'csv') => void;
}

export function ExportButtons({ onExport, isExporting, format, onFormatChange }: ExportButtonsProps) {
  const formats = [
    { value: 'pdf', label: 'PDF', icon: FileText },
    { value: 'excel', label: 'Excel', icon: FileSpreadsheet },
    { value: 'csv', label: 'CSV', icon: File }
  ];

  return (
    <div className="flex items-center space-x-3">
      <Select value={format} onValueChange={onFormatChange}>
        <SelectTrigger className="w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {formats.map((fmt) => (
            <SelectItem key={fmt.value} value={fmt.value}>
              <div className="flex items-center space-x-2">
                <fmt.icon className="h-4 w-4" />
                <span>{fmt.label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Button
        onClick={onExport}
        disabled={isExporting}
        className="bg-lupizza-green-600 hover:bg-lupizza-green-700"
      >
        {isExporting ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Exporting...
          </>
        ) : (
          <>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </>
        )}
      </Button>
    </div>
  );
}