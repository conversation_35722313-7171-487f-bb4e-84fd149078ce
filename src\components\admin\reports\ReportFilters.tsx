"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TrendingUp, ChefHat, Users } from 'lucide-react';

interface ReportFiltersProps {
  reportType: 'sales' | 'menu' | 'staff';
  onReportTypeChange: (type: 'sales' | 'menu' | 'staff') => void;
}

export function ReportFilters({ reportType, onReportTypeChange }: ReportFiltersProps) {
  const reportTypes = [
    {
      value: 'sales',
      label: 'Sales Report',
      description: 'Revenue, orders, and transaction data',
      icon: TrendingUp
    },
    {
      value: 'menu',
      label: 'Menu Performance',
      description: 'Best selling items and category analysis',
      icon: ChefHat
    },
    {
      value: 'staff',
      label: 'Staff Performance',
      description: 'Employee productivity and sales metrics',
      icon: Users
    }
  ];

  return (
    <div className="flex items-center space-x-3">
      <span className="text-sm font-medium text-slate-700">Report Type:</span>
      <Select value={reportType} onValueChange={onReportTypeChange}>
        <SelectTrigger className="w-64">
          <SelectValue placeholder="Select report type" />
        </SelectTrigger>
        <SelectContent>
          {reportTypes.map((type) => (
            <SelectItem key={type.value} value={type.value}>
              <div className="flex items-center space-x-3">
                <type.icon className="h-4 w-4 text-lupizza-green-600" />
                <div>
                  <div className="font-medium">{type.label}</div>
                  <div className="text-xs text-slate-600">{type.description}</div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}