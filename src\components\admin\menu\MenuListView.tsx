"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useDeleteMenu, useUpdateMenu } from '@/hooks/useMenu';
import { Menu } from '@/types';
import { Edit, Trash2, Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';

interface MenuListViewProps {
  menus: Menu[];
  onEdit: (menu: Menu) => void;
}

export function MenuListView({ menus, onEdit }: MenuListViewProps) {
  const deleteMenu = useDeleteMenu();
  const updateMenu = useUpdateMenu();

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this menu item?')) {
      try {
        await deleteMenu.mutateAsync(id);
        toast.success('Menu item deleted successfully');
      } catch (error) {
        toast.error('Failed to delete menu item');
      }
    }
  };

  const handleToggleAvailability = async (menu: Menu) => {
    try {
      await updateMenu.mutateAsync({
        id: menu.id,
        updates: { available: !menu.available }
      });
      toast.success(`Menu item ${menu.available ? 'disabled' : 'enabled'}`);
    } catch (error) {
      toast.error('Failed to update menu item');
    }
  };

  return (
    <div className="space-y-4">
      {menus.map((menu) => (
        <Card key={menu.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              {/* Image */}
              <div className="relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                {menu.image_url ? (
                  <Image
                    src={menu.image_url}
                    alt={menu.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-green-100 to-slate-100 flex items-center justify-center">
                    <span className="text-green-600 text-xs font-medium">No Image</span>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-slate-900 truncate">
                      {menu.name}
                    </h3>
                    <p className="text-sm text-slate-600 mt-1 line-clamp-2">
                      {menu.description}
                    </p>
                    <div className="flex items-center space-x-3 mt-3">
                      <Badge variant="secondary" className="text-xs">
                        {menu.category.charAt(0).toUpperCase() + menu.category.slice(1)}
                      </Badge>
                      <Badge variant={menu.available ? "default" : "secondary"} className="text-xs">
                        {menu.available ? "Available" : "Unavailable"}
                      </Badge>
                    </div>
                  </div>

                  {/* Price and Actions */}
                  <div className="flex items-center space-x-4 ml-4">
                    <div className="text-right">
                      <span className="text-xl font-bold text-green-600">
                        {new Intl.NumberFormat('id-ID', { 
                          style: 'currency', 
                          currency: 'IDR' 
                        }).format(menu.price)}
                      </span>
                    </div>

                    {/* Availability Toggle */}
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={menu.available}
                        onCheckedChange={() => handleToggleAvailability(menu)}
                        className=" border-2 border-slate-400"
                      />
                      {menu.available ? (
                        <Eye className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-slate-400" />
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(menu)}
                        className="hover:bg-blue-50 hover:border-blue-200"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(menu.id)}
                        className="hover:bg-red-50 hover:border-red-200 text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
