'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Plus,
  Users,
  FileText,
  Settings,
  Package,
  BarChart3,
  Download,
  Eye,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export function QuickActions() {
  const router = useRouter();

  const actions = [
    {
      title: 'Add Menu Item',
      description: 'Create new food or beverage item',
      icon: Plus,
      color: 'bg-green-500 hover:bg-green-600',
      onClick: () => router.push('/dashboard/admin/menu'),
    },
    {
      title: 'Manage Users',
      description: 'Add or edit staff accounts',
      icon: Users,
      color: 'bg-blue-500 hover:bg-blue-600',
      onClick: () => router.push('/dashboard/admin/users'),
    },
    {
      title: 'View Reports',
      description: 'Generate sales and analytics reports',
      icon: FileText,
      color: 'bg-purple-500 hover:bg-purple-600',
      onClick: () => router.push('/dashboard/admin/reports'),
    },
    {
      title: 'System Settings',
      description: 'Configure restaurant settings',
      icon: Settings,
      color: 'bg-gray-500 hover:bg-gray-600',
      onClick: () => router.push('/dashboard/admin/settings'),
    },
    {
      title: 'View Orders',
      description: 'Monitor all restaurant orders',
      icon: Eye,
      color: 'bg-orange-500 hover:bg-orange-600',
      onClick: () => router.push('/dashboard/admin/orders'),
    },
    {
      title: 'Analytics',
      description: 'Detailed performance analytics',
      icon: BarChart3,
      color: 'bg-indigo-500 hover:bg-indigo-600',
      onClick: () => router.push('/dashboard/admin/analytics'),
    },
  ];

  return (
    <Card className="bg-white border border-slate-200 shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-900">
          Quick Actions
        </CardTitle>
        <p className="text-sm text-slate-600">Common administrative tasks</p>
      </CardHeader>
      <CardContent className="space-y-3">
        {actions.map((action, index) => (
          <button
            key={index}
            className="w-full p-3 flex items-center space-x-3 rounded-lg border border-slate-200 bg-white hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 text-left"
            onClick={action.onClick}
          >
            <div
              className={`w-10 h-10 rounded-lg flex items-center justify-center text-white flex-shrink-0 ${action.color}`}
            >
              <action.icon className="h-5 w-5" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-slate-900 text-sm">
                {action.title}
              </p>
              <p className="text-xs text-slate-600 truncate">
                {action.description}
              </p>
            </div>
          </button>
        ))}
      </CardContent>
    </Card>
  );
}
