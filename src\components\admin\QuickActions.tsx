"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Plus, 
  Users, 
  FileText, 
  Settings, 
  Package,
  BarChart3,
  Download,
  Eye
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export function QuickActions() {
  const router = useRouter();

  const actions = [
    {
      title: 'Add Menu Item',
      description: 'Create new food or beverage item',
      icon: Plus,
      color: 'bg-green-500 hover:bg-green-600',
      onClick: () => router.push('/dashboard/admin/menu'),
    },
    {
      title: 'Manage Users',
      description: 'Add or edit staff accounts',
      icon: Users,
      color: 'bg-blue-500 hover:bg-blue-600',
      onClick: () => router.push('/dashboard/admin/users'),
    },
    {
      title: 'View Reports',
      description: 'Generate sales and analytics reports',
      icon: FileText,
      color: 'bg-purple-500 hover:bg-purple-600',
      onClick: () => router.push('/dashboard/admin/reports'),
    },
    {
      title: 'System Settings',
      description: 'Configure restaurant settings',
      icon: Settings,
      color: 'bg-gray-500 hover:bg-gray-600',
      onClick: () => router.push('/dashboard/admin/settings'),
    },
    {
      title: 'View Orders',
      description: 'Monitor all restaurant orders',
      icon: Eye,
      color: 'bg-orange-500 hover:bg-orange-600',
      onClick: () => router.push('/dashboard/admin/orders'),
    },
    {
      title: 'Analytics',
      description: 'Detailed performance analytics',
      icon: BarChart3,
      color: 'bg-indigo-500 hover:bg-indigo-600',
      onClick: () => router.push('/dashboard/admin/analytics'),
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-900">
          Quick Actions
        </CardTitle>
        <p className="text-sm text-slate-600">
          Common administrative tasks
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2 hover:shadow-md transition-all duration-200"
              onClick={action.onClick}
            >
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white ${action.color}`}>
                <action.icon className="h-4 w-4" />
              </div>
              <div className="text-left">
                <p className="font-medium text-slate-900">{action.title}</p>
                <p className="text-xs text-slate-600">{action.description}</p>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
