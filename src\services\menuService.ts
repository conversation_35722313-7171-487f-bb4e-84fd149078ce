import { supabase } from '@/lib/supabase';
import { MenuItem, MenuItemWithVariants, Category, ItemVariant } from '@/types';

export const menuService = {
  async getMenuItems(): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        categories (
          id,
          name,
          description
        )
      `)
      .order('sort_order', { ascending: true })
      .order('name', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async getMenuItemsByCategory(categoryId: string): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        categories (
          id,
          name,
          description
        )
      `)
      .eq('category_id', categoryId)
      .eq('is_available', true)
      .order('sort_order', { ascending: true })
      .order('name', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async getMenuItemsWithVariants(): Promise<MenuItemWithVariants[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select(`
        *,
        categories (
          id,
          name,
          description
        ),
        item_variants (
          id,
          variant_type,
          variant_name,
          price_adjustment,
          is_default,
          sort_order
        )
      `)
      .eq('is_available', true)
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async getCategories(): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async createMenuItem(menuData: {
    name: string;
    description?: string;
    category_id: string;
    base_price: number;
    price_grab?: number;
    price_gojek?: number;
    image_url?: string;
    is_available?: boolean;
    is_beverage?: boolean;
    sort_order?: number;
  }): Promise<MenuItem> {
    try {
      const { data, error } = await supabase
        .from('menu_items')
        .insert({
          ...menuData,
          is_available: menuData.is_available ?? true,
          is_beverage: menuData.is_beverage ?? false,
          sort_order: menuData.sort_order ?? 0,
        })
        .select(`
          *,
          categories (
            id,
            name,
            description
          )
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create menu item error:', error);
      throw error;
    }
  },

  async createCategory(categoryData: {
    name: string;
    description?: string;
    sort_order?: number;
  }): Promise<Category> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          ...categoryData,
          sort_order: categoryData.sort_order ?? 0,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create category error:', error);
      throw error;
    }
  },

  async createItemVariant(variantData: {
    menu_item_id: string;
    variant_type: string;
    variant_name: string;
    price_adjustment?: number;
    is_default?: boolean;
    sort_order?: number;
  }): Promise<ItemVariant> {
    try {
      const { data, error } = await supabase
        .from('item_variants')
        .insert({
          ...variantData,
          price_adjustment: variantData.price_adjustment ?? 0,
          is_default: variantData.is_default ?? false,
          sort_order: variantData.sort_order ?? 0,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create item variant error:', error);
      throw error;
    }
  },

  async updateMenuItem(id: string, updates: Partial<MenuItem>): Promise<MenuItem> {
    try {
      const updateData: any = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('menu_items')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          categories (
            id,
            name,
            description
          )
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update menu item error:', error);
      throw error;
    }
  },

  async updateCategory(id: string, updates: Partial<Category>): Promise<Category> {
    try {
      const updateData: any = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('categories')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update category error:', error);
      throw error;
    }
  },

  async deleteMenuItem(id: string): Promise<void> {
    const { error } = await supabase
      .from('menu_items')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async deleteCategory(id: string): Promise<void> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async deleteItemVariant(id: string): Promise<void> {
    const { error } = await supabase
      .from('item_variants')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async getItemVariants(menuItemId: string): Promise<ItemVariant[]> {
    const { data, error } = await supabase
      .from('item_variants')
      .select('*')
      .eq('menu_item_id', menuItemId)
      .order('variant_type', { ascending: true })
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },
};