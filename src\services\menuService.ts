import { supabase } from '@/lib/supabase';
import { Menu } from '@/types';

export const menuService = {
  async getMenus(): Promise<Menu[]> {
    const { data, error } = await supabase
      .from('menus')
      .select('*')
      .order('category', { ascending: true })
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async getMenusByCategory(category: string): Promise<Menu[]> {
    const { data, error } = await supabase
      .from('menus')
      .select('*')
      .eq('category', category)
      .eq('available', true)
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async createMenu(menuData: Omit<Menu, 'id' | 'created_at' | 'updated_at'> & { image_file?: File | null }): Promise<Menu> {
    try {
      let imageUrl = '';

      // Handle image upload if provided
      if (menuData.image_file) {
        const fileExt = menuData.image_file.name.split('.').pop();
        const fileName = `${Date.now()}.${fileExt}`;

        console.log('Uploading file:', fileName, 'Size:', menuData.image_file.size);

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('menu-images')
          .upload(fileName, menuData.image_file);

        if (uploadError) {
          console.error('Upload error:', uploadError);
          throw new Error(`Failed to upload image: ${uploadError.message}`);
        }

        if (uploadData) {
          const { data: { publicUrl } } = supabase.storage
            .from('menu-images')
            .getPublicUrl(fileName);
          imageUrl = publicUrl;
          console.log('Image uploaded successfully:', publicUrl);
        }
      }

      // Remove image_file from the data and add image_url
      const { image_file, ...menuWithoutFile } = menuData;
      const menuToInsert = {
        ...menuWithoutFile,
        image_url: imageUrl,
      };

      const { data, error } = await supabase
        .from('menus')
        .insert(menuToInsert)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create menu error:', error);
      throw error;
    }
  },

  async updateMenu(id: string, updates: Partial<Menu> & { image_file?: File | null }): Promise<Menu> {
    try {
      let imageUrl = undefined;

      // Handle image upload if provided
      if (updates.image_file) {
        const fileExt = updates.image_file.name.split('.').pop();
        const fileName = `${Date.now()}.${fileExt}`;

        console.log('Updating image, uploading file:', fileName);

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('menu-images')
          .upload(fileName, updates.image_file);

        if (uploadError) {
          console.error('Upload error during update:', uploadError);
          throw new Error(`Failed to upload image: ${uploadError.message}`);
        }

        if (uploadData) {
          const { data: { publicUrl } } = supabase.storage
            .from('menu-images')
            .getPublicUrl(fileName);
          imageUrl = publicUrl;
          console.log('Image updated successfully:', publicUrl);
        }
      }

      // Remove image_file from updates and add image_url if needed
      const { image_file, ...updatesWithoutFile } = updates;
      const updateData: any = {
        ...updatesWithoutFile,
        updated_at: new Date().toISOString(),
      };

      if (imageUrl !== undefined) {
        updateData.image_url = imageUrl;
      }

      const { data, error } = await supabase
        .from('menus')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update menu error:', error);
      throw error;
    }
  },

  async deleteMenu(id: string): Promise<void> {
    const { error } = await supabase
      .from('menus')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  async getCategories(): Promise<string[]> {
    const { data, error } = await supabase
      .from('menus')
      .select('category')
      .order('category', { ascending: true });
    
    if (error) throw error;
    
    const categories = Array.from(new Set(data?.map(item => item.category) || []));
    return categories;
  },
};