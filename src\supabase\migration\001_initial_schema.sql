-- =============================================
-- Lupizza POS Database Schema
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 1. BRANCHES TABLE
-- =============================================
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 2. USERS TABLE (extends Supabase auth.users)
-- =============================================
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL UNIQUE,
    full_name VARCHAR(255),
    role VARCHAR(20) NOT NULL CHECK (
        role IN ('admin', 'cashier', 'kitchen')
    ),
    branch_id UUID REFERENCES branches (id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 3. CATEGORIES TABLE
-- =============================================
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 4. MENU ITEMS TABLE
-- =============================================
CREATE TABLE menu_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID NOT NULL REFERENCES categories (id) ON DELETE CASCADE,
    base_price DECIMAL(10, 2) NOT NULL DEFAULT 0,
    price_grab DECIMAL(10, 2),
    price_gojek DECIMAL(10, 2),
    image_url TEXT,
    is_available BOOLEAN DEFAULT true,
    is_beverage BOOLEAN DEFAULT false, -- untuk minuman yang langsung diproses kasir
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 5. ITEM VARIANTS TABLE (untuk ukuran, suhu, dll)
-- =============================================
CREATE TABLE item_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    menu_item_id UUID NOT NULL REFERENCES menu_items (id) ON DELETE CASCADE,
    variant_type VARCHAR(50) NOT NULL, -- 'size', 'temperature', 'sauce', 'portion'
    variant_name VARCHAR(100) NOT NULL, -- 'S', 'M', 'L', 'Hot', 'Ice', etc.
    price_adjustment DECIMAL(10, 2) DEFAULT 0, -- tambahan/pengurangan harga
    is_default BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        UNIQUE (
            menu_item_id,
            variant_type,
            variant_name
        )
);

-- =============================================
-- 6. PROMOTIONS TABLE
-- =============================================
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    promo_type VARCHAR(20) NOT NULL CHECK (
        promo_type IN ('category', 'item', 'total')
    ),
    discount_type VARCHAR(20) NOT NULL CHECK (
        discount_type IN ('percentage', 'fixed')
    ),
    discount_value DECIMAL(10, 2) NOT NULL,
    target_category_id UUID REFERENCES categories (id) ON DELETE CASCADE,
    target_item_id UUID REFERENCES menu_items (id) ON DELETE CASCADE,
    min_purchase DECIMAL(10, 2) DEFAULT 0,
    start_date TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        end_date TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 7. ORDERS TABLE
-- =============================================
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    order_number VARCHAR(50) NOT NULL UNIQUE,
    order_date TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        order_type VARCHAR(20) NOT NULL CHECK (
            order_type IN (
                'dine_in',
                'take_away',
                'delivery_grab',
                'delivery_gojek'
            )
        ),
        table_number VARCHAR(10),
        customer_name VARCHAR(255),
        customer_phone VARCHAR(20),
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (
            status IN (
                'pending',
                'processing',
                'ready',
                'completed',
                'cancelled'
            )
        ),
        subtotal DECIMAL(10, 2) NOT NULL DEFAULT 0,
        discount_amount DECIMAL(10, 2) DEFAULT 0,
        tax_amount DECIMAL(10, 2) DEFAULT 0,
        total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
        notes TEXT,
        cashier_id UUID NOT NULL REFERENCES users (id),
        branch_id UUID NOT NULL REFERENCES branches (id),
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 8. ORDER ITEMS TABLE
-- =============================================
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    order_id UUID NOT NULL REFERENCES orders (id) ON DELETE CASCADE,
    menu_item_id UUID NOT NULL REFERENCES menu_items (id),
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10, 2) NOT NULL, -- harga saat order dibuat
    total_price DECIMAL(10, 2) NOT NULL,
    notes TEXT, -- catatan khusus per item
    variant_selections JSONB, -- menyimpan pilihan varian dalam format JSON
    is_prepared BOOLEAN DEFAULT false, -- untuk tracking di dapur
    prepared_at TIMESTAMP
    WITH
        TIME ZONE,
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 9. TRANSACTIONS TABLE
-- =============================================
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    order_id UUID NOT NULL REFERENCES orders (id) ON DELETE CASCADE,
    payment_method VARCHAR(20) NOT NULL CHECK (
        payment_method IN (
            'cash',
            'qris',
            'card',
            'split'
        )
    ),
    amount_paid DECIMAL(10, 2) NOT NULL,
    change_amount DECIMAL(10, 2) DEFAULT 0,
    payment_details JSONB, -- untuk menyimpan detail pembayaran split, dll
    transaction_date TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 10. ACTIVITY LOGS TABLE
-- =============================================
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
    user_id UUID NOT NULL REFERENCES users (id),
    action VARCHAR(100) NOT NULL,
    description TEXT,
    entity_type VARCHAR(50), -- 'order', 'menu', 'user', etc.
    entity_id UUID,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX idx_users_role ON users (role);

CREATE INDEX idx_users_branch_id ON users (branch_id);

CREATE INDEX idx_users_email ON users (email);

CREATE INDEX idx_menu_items_category_id ON menu_items (category_id);

CREATE INDEX idx_menu_items_is_available ON menu_items (is_available);

CREATE INDEX idx_menu_items_is_beverage ON menu_items (is_beverage);

CREATE INDEX idx_item_variants_menu_item_id ON item_variants (menu_item_id);

CREATE INDEX idx_item_variants_type ON item_variants (variant_type);

CREATE INDEX idx_orders_status ON orders (status);

CREATE INDEX idx_orders_order_type ON orders (order_type);

CREATE INDEX idx_orders_cashier_id ON orders (cashier_id);

CREATE INDEX idx_orders_branch_id ON orders (branch_id);

CREATE INDEX idx_orders_order_date ON orders (order_date);

CREATE INDEX idx_orders_order_number ON orders (order_number);

CREATE INDEX idx_order_items_order_id ON order_items (order_id);

CREATE INDEX idx_order_items_menu_item_id ON order_items (menu_item_id);

CREATE INDEX idx_order_items_is_prepared ON order_items (is_prepared);

CREATE INDEX idx_transactions_order_id ON transactions (order_id);

CREATE INDEX idx_transactions_payment_method ON transactions (payment_method);

CREATE INDEX idx_activity_logs_user_id ON activity_logs (user_id);

CREATE INDEX idx_activity_logs_action ON activity_logs (action);

CREATE INDEX idx_activity_logs_entity_type ON activity_logs (entity_type);

CREATE INDEX idx_activity_logs_created_at ON activity_logs (created_at);

CREATE INDEX idx_promotions_active ON promotions (
    is_active,
    start_date,
    end_date
);

CREATE INDEX idx_promotions_category ON promotions (target_category_id);

CREATE INDEX idx_promotions_item ON promotions (target_item_id);

-- =============================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_item_variants_updated_at BEFORE UPDATE ON item_variants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotions_updated_at BEFORE UPDATE ON promotions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_order_items_updated_at BEFORE UPDATE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();