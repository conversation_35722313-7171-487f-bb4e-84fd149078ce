"use client";

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Users, Clock, Star } from 'lucide-react';

interface CustomerInsightsProps {
  orders: any[];
}

export function CustomerInsights({ orders }: CustomerInsightsProps) {
  // Calculate insights
  const totalOrders = orders.length;
  const completedOrders = orders.filter(o => o.status === 'completed').length;
  const avgOrderValue = totalOrders > 0 ? orders.reduce((sum, o) => sum + o.total, 0) / totalOrders : 0;
  
  // Peak hours analysis
  const hourlyOrders = orders.reduce((acc, order) => {
    const hour = new Date(order.created_at).getHours();
    acc[hour] = (acc[hour] || 0) + 1;
    return acc;
  }, {} as Record<number, number>);

  const peakHour = (Object.entries(hourlyOrders) as [string, number][])
    .sort(([,a], [,b]) => b - a)[0];

  const insights = [
    {
      title: 'Order Completion Rate',
      value: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
      format: 'percentage',
      icon: Star,
      color: 'lupizza-green'
    },
    {
      title: 'Average Order Value',
      value: avgOrderValue,
      format: 'currency',
      icon: Users,
      color: 'lupizza-cream'
    },
    {
      title: 'Peak Hour',
      value: peakHour ? `${peakHour[0]}:00` : 'N/A',
      format: 'text',
      icon: Clock,
      color: 'lupizza-red',
      subtitle: peakHour ? `${peakHour[1]} orders` : ''
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-medium">Customer Insights</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {insights.map((insight, index) => (
          <div key={index} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <insight.icon className={`h-4 w-4 text-${insight.color}-600`} />
                <span className="text-sm font-medium text-slate-700">
                  {insight.title}
                </span>
              </div>
              <div className="text-right">
                <span className="text-lg font-bold text-slate-900">
                  {insight.format === 'currency' 
                    ? new Intl.NumberFormat('id-ID', { 
                        style: 'currency', 
                        currency: 'IDR' 
                      }).format(insight.value as number)
                    : insight.format === 'percentage'
                    ? `${(insight.value as number).toFixed(1)}%`
                    : insight.value
                  }
                </span>
                {insight.subtitle && (
                  <p className="text-xs text-slate-500">{insight.subtitle}</p>
                )}
              </div>
            </div>
            {insight.format === 'percentage' && (
              <Progress 
                value={insight.value as number} 
                className="h-2"
              />
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}