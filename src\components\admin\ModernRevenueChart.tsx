"use client";

import { Modern<PERSON><PERSON> } from '@/components/ui/ModernChart';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Order } from '@/types';
import { useMemo } from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface ModernRevenueChartProps {
  orders: Order[];
  className?: string;
}

export function ModernRevenueChart({ orders, className }: ModernRevenueChartProps) {
  const { chartData, trend } = useMemo(() => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date;
    }).reverse();

    const data = last7Days.map((date) => {
      const dayOrders = orders.filter((order) => {
        const orderDate = new Date(order.created_at);
        return orderDate.toDateString() === date.toDateString();
      });

      const revenue = dayOrders.reduce((sum, order) => sum + order.total, 0);

      return {
        date: date.toLocaleDateString('id-ID', {
          weekday: 'short',
          day: 'numeric',
        }),
        revenue,
        orders: dayOrders.length,
      };
    });

    // Calculate trend
    const totalRevenue = data.reduce((sum, day) => sum + day.revenue, 0);
    const avgRevenue = totalRevenue / data.length;
    const lastDayRevenue = data[data.length - 1]?.revenue || 0;
    const trendPercentage = avgRevenue > 0 ? ((lastDayRevenue - avgRevenue) / avgRevenue * 100) : 0;

    return {
      chartData: data,
      trend: {
        percentage: Math.abs(trendPercentage).toFixed(1),
        isPositive: trendPercentage >= 0,
        total: totalRevenue
      }
    };
  }, [orders]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-slate-200 rounded-lg shadow-lg">
          <div className="font-medium text-slate-900 mb-2">{label}</div>
          <div className="space-y-1 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-slate-600">Revenue:</span>
              <span className="font-semibold text-green-600">
                Rp {data.revenue.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-600">Orders:</span>
              <span className="font-semibold text-slate-900">{data.orders}</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const headerAction = (
    <div className="flex items-center space-x-2">
      <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
        trend.isPositive 
          ? 'bg-green-50 text-green-700 border border-green-200/50' 
          : 'bg-red-50 text-red-700 border border-red-200/50'
      }`}>
        {trend.isPositive ? (
          <TrendingUp className="w-3 h-3" />
        ) : (
          <TrendingDown className="w-3 h-3" />
        )}
        <span>{trend.percentage}%</span>
      </div>
    </div>
  );

  const isEmpty = chartData.length === 0 || chartData.every(day => day.revenue === 0);

  return (
    <ModernChart
      title="Revenue Trend"
      subtitle={`Total: Rp ${trend.total.toLocaleString('id-ID')} (Last 7 days)`}
      className={className}
      headerAction={headerAction}
      isEmpty={isEmpty}
      emptyMessage="No revenue data available"
    >
      {!isEmpty && (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis 
              dataKey="date" 
              stroke="#64748b"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              stroke="#64748b"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${(value / 1000)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Line 
              type="monotone" 
              dataKey="revenue" 
              stroke="#22c55e" 
              strokeWidth={3}
              dot={false}
              activeDot={{ 
                r: 6, 
                stroke: '#22c55e', 
                strokeWidth: 2, 
                fill: '#22c55e',
                filter: 'drop-shadow(0 2px 4px rgba(34, 197, 94, 0.3))'
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      )}
    </ModernChart>
  );
}
