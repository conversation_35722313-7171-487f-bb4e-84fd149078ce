import { StatCard } from '@/components/ui/StatCard';
import { DollarSign, ShoppingCart, Users, TrendingUp } from 'lucide-react';
import { Order } from '@/types';
import { useMemo } from 'react';

interface AdminStatsProps {
  orders: Order[];
}

export function AdminStats({ orders }: AdminStatsProps) {
  const stats = useMemo(() => {
    const today = new Date();
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);

    const todayOrders = orders.filter(
      (order) => new Date(order.order_date) >= todayStart
    );
    const weekOrders = orders.filter(
      (order) => new Date(order.order_date) >= weekStart
    );
    const monthOrders = orders.filter(
      (order) => new Date(order.order_date) >= monthStart
    );
    const pendingOrders = orders.filter((order) => order.status === 'pending');

    return {
      todayRevenue: todayOrders.reduce(
        (sum, order) => sum + order.total_amount,
        0
      ),
      weeklyRevenue: weekOrders.reduce(
        (sum, order) => sum + order.total_amount,
        0
      ),
      monthlyRevenue: monthOrders.reduce(
        (sum, order) => sum + order.total_amount,
        0
      ),
      todayOrders: todayOrders.length,
      weeklyOrders: weekOrders.length,
      monthlyOrders: monthOrders.length,
      pendingOrders: pendingOrders.length,
      totalCustomers: new Set(
        orders.map((order) => order.customer_name || order.id)
      ).size,
      averageOrderValue:
        orders.length > 0
          ? orders.reduce((sum, order) => sum + order.total_amount, 0) /
            orders.length
          : 0,
    };
  }, [orders]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="Today's Revenue"
        value={stats.todayRevenue}
        format="currency"
        icon={DollarSign}
        trend="up"
        change={12.5}
      />
      <StatCard
        title="Total Orders"
        value={stats.todayOrders}
        format="number"
        icon={ShoppingCart}
        trend="up"
        change={8.2}
      />
      <StatCard
        title="Total Customers"
        value={stats.totalCustomers}
        format="number"
        icon={Users}
        trend="down"
        change={2.1}
      />
      <StatCard
        title="Avg Order Value"
        value={Math.round(stats.averageOrderValue)}
        format="currency"
        icon={TrendingUp}
        trend="up"
        change={15.3}
      />
    </div>
  );
}
