'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { authService } from '@/services/authService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Pizza, Loader2, Eye, EyeOff, ChefHat, Sparkles } from 'lucide-react';
import toast from 'react-hot-toast';
import { useQueryClient } from '@tanstack/react-query';

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Clear cache when component mounts
  useEffect(() => {
    queryClient.clear();
  }, [queryClient]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      // Clear any existing cache
      queryClient.clear();
      
      await authService.signIn(data.email, data.password);
      const user = await authService.getCurrentUser();

      if (user) {
        const profile = await authService.getUserProfile(user.id);
        if (profile) {
          // Force refetch user data
          queryClient.invalidateQueries({ queryKey: ['user'] });
          
          switch (profile.role) {
            case 'admin':
              router.push('/dashboard/admin');
              break;
            case 'cashier':
              router.push('/dashboard/cashier');
              break;
            case 'kitchen':
              router.push('/dashboard/kitchen');
              break;
            default:
              toast.error('Invalid user role');
          }
        }
      }
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-lupizza-green-50 via-lupizza-cream-50 to-lupizza-red-50 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decorative elements */}
      {/* <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-lupizza-green-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-lupizza-red-200/30 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-lupizza-cream-100/20 rounded-full blur-3xl"></div>
        <div className="pizza-pattern absolute inset-0"></div>
      </div> */}

      {/* Main content */}
      <div className="relative z-10 w-full max-w-md">
        <Card className=" bg-white/90 border-0 shadow-2xl shadow-lupizza-green-500/10">
          <CardHeader className="text-center pb-8 pt-8">
            <div className="mb-6 relative">
              <div className="absolute inset-0 bg-lupizza-gradient rounded-full blur-lg opacity-30 scale-110"></div>
              <div className="relative bg-lupizza-gradient w-16 h-16 mx-auto rounded-2xl flex items-center justify-center shadow-lupizza">
                <Pizza className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-800">
              LuPiZza POS
            </CardTitle>
            <CardDescription className="text-slate-600 mt-2 text-base">
              Welcome back! Please sign in to continue
            </CardDescription>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-700 font-medium">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  {...register('email')}
                  className={`h-12 bg-white/50 border-slate-200 focus:border-lupizza-green-400 focus:ring-lupizza-green-400/20 transition-all duration-200 ${
                    errors.email
                      ? 'border-lupizza-red-400 focus:border-lupizza-red-400 focus:ring-lupizza-red-400/20'
                      : ''
                  }`}
                />
                {errors.email && (
                  <p className="text-sm text-lupizza-red-500 flex items-center gap-1">
                    <span className="w-1 h-1 bg-lupizza-red-500 rounded-full"></span>
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="password"
                  className="text-slate-700 font-medium"
                >
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    {...register('password')}
                    className={`h-12 bg-white/50 border-slate-200 focus:border-lupizza-green-400 focus:ring-lupizza-green-400/20 transition-all duration-200 pr-12 ${
                      errors.password
                        ? 'border-lupizza-red-400 focus:border-lupizza-red-400 focus:ring-lupizza-red-400/20'
                        : ''
                    }`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-slate-400 hover:text-lupizza-green-600 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-lupizza-red-500 flex items-center gap-1">
                    <span className="w-1 h-1 bg-lupizza-red-500 rounded-full"></span>
                    {errors.password.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-lupizza-gradient hover:shadow-lupizza-lg text-white font-semibold shadow-lupizza transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-5 w-5" />
                    Sign In
                  </>
                )}
              </Button>
            </form>

            {/* Demo accounts section */}
            <div className="mt-8 p-6 bg-gradient-to-r from-lupizza-green-50 to-lupizza-cream-50 rounded-2xl border border-lupizza-green-200/50">
              <div className="flex items-center gap-2 mb-4">
                <ChefHat className="h-5 w-5 text-lupizza-green-600" />
                <p className="text-sm font-semibold text-lupizza-green-700">
                  Demo Accounts
                </p>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-white/60 rounded-xl border border-lupizza-green-200/50">
                  <div>
                    <p className="text-sm font-medium text-lupizza-green-700">
                      Admin
                    </p>
                    <p className="text-xs text-lupizza-green-500">
                      <EMAIL>
                    </p>
                  </div>
                  <div className="text-xs text-lupizza-green-400 font-mono">
                    password123
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 bg-white/60 rounded-xl border border-lupizza-green-200/50">
                  <div>
                    <p className="text-sm font-medium text-lupizza-green-700">
                      Cashier
                    </p>
                    <p className="text-xs text-lupizza-green-500">
                      <EMAIL>
                    </p>
                  </div>
                  <div className="text-xs text-lupizza-green-400 font-mono">
                    password123
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 bg-white/60 rounded-xl border border-lupizza-green-200/50">
                  <div>
                    <p className="text-sm font-medium text-lupizza-green-700">
                      Kitchen
                    </p>
                    <p className="text-xs text-lupizza-green-500">
                      <EMAIL>
                    </p>
                  </div>
                  <div className="text-xs text-lupizza-green-400 font-mono">
                    password123
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
