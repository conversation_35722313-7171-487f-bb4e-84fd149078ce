import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OrderTimerProps {
  createdAt: string;
  className?: string;
}

export function OrderTimer({ createdAt, className }: OrderTimerProps) {
  const [timeElapsed, setTimeElapsed] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date().getTime();
      const orderTime = new Date(createdAt).getTime();
      const elapsed = Math.floor((now - orderTime) / (1000 * 60)); // in minutes
      setTimeElapsed(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [createdAt]);

  const getTimerColor = () => {
    if (timeElapsed > 15) return 'bg-lupizza-red-500 text-white';
    if (timeElapsed > 10) return 'bg-yellow-500 text-white';
    return 'bg-lupizza-green-500 text-white';
  };

  const getTimerIcon = () => {
    if (timeElapsed > 15) return AlertTriangle;
    return Clock;
  };

  const Icon = getTimerIcon();

  return (
    <Badge className={cn(getTimerColor(), 'flex items-center space-x-1', className)}>
      <Icon className="h-3 w-3" />
      <span>{timeElapsed}m</span>
    </Badge>
  );
}