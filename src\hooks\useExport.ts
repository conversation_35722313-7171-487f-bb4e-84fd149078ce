"use client";

import { useState } from 'react';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

interface ExportParams {
  type: 'sales' | 'menu' | 'staff';
  format: 'pdf' | 'excel' | 'csv';
  dateRange: { from: Date; to: Date };
  data: any;
}

const exportService = {
  async exportToCsv(data: any, filename: string): Promise<void> {
    let csvContent = '';
    
    if (data.sales) {
      csvContent = 'Date,Revenue,Orders\n';
      data.sales.dailyBreakdown?.forEach((day: any) => {
        csvContent += `${day.date},${day.revenue},${day.orders}\n`;
      });
    } else if (data.menu) {
      csvContent = 'Item Name,Category,Total Sold,Revenue\n';
      data.menu.topPerformers?.forEach((item: any) => {
        csvContent += `${item.name},${item.category},${item.totalSold},${item.revenue}\n`;
      });
    } else if (data.staff) {
      csvContent = 'Name,Role,Orders Processed,Revenue\n';
      data.staff.performance?.forEach((staff: any) => {
        csvContent += `${staff.name},${staff.role},${staff.ordersProcessed},${staff.revenue}\n`;
      });
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  async exportToExcel(data: any, filename: string): Promise<void> {
    // For now, we'll export as CSV since Excel export requires additional libraries
    // In a real implementation, you'd use libraries like xlsx or exceljs
    await this.exportToCsv(data, filename.replace('.xlsx', '.csv'));
    toast.info('Exported as CSV format (Excel export requires additional setup)');
  },

  async exportToPdf(data: any, filename: string): Promise<void> {
    // For now, we'll show a message since PDF export requires additional libraries
    // In a real implementation, you'd use libraries like jsPDF or react-pdf
    toast.info('PDF export requires additional setup. Please use CSV or Excel format for now.');
  }
};

export const useExport = () => {
  const [isExporting, setIsExporting] = useState(false);

  const exportReport = async (params: ExportParams) => {
    setIsExporting(true);
    
    try {
      const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
      const dateRangeStr = `${format(params.dateRange.from, 'yyyy-MM-dd')}_to_${format(params.dateRange.to, 'yyyy-MM-dd')}`;
      const filename = `${params.type}_report_${dateRangeStr}_${timestamp}`;

      switch (params.format) {
        case 'csv':
          await exportService.exportToCsv(params.data, `${filename}.csv`);
          break;
        case 'excel':
          await exportService.exportToExcel(params.data, `${filename}.xlsx`);
          break;
        case 'pdf':
          await exportService.exportToPdf(params.data, `${filename}.pdf`);
          break;
        default:
          throw new Error(`Unsupported export format: ${params.format}`);
      }

      toast.success(`Report exported successfully as ${params.format.toUpperCase()}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export report');
      throw error;
    } finally {
      setIsExporting(false);
    }
  };

  return {
    exportReport,
    isExporting
  };
};
