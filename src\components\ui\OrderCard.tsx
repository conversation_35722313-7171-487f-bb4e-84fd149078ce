import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { OrderItemStatus, OrderWithItems } from '@/types';
import { Clock, MapPin, User, DollarSign } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OrderCardProps {
  order: OrderWithItems;
  onStatusChange?: (orderId: string, status: "pending" | "processing" | "ready" | "completed" | "cancelled") => void;
  onItemStatusChange?: (itemId: string, status: OrderItemStatus) => void;
  showActions?: boolean;
  variant?: 'kitchen' | 'cashier' | 'admin';
  className?: string;
}

export function OrderCard({ 
  order, 
  onStatusChange, 
  onItemStatusChange,
  showActions = true,
  variant = 'kitchen',
  className 
}: OrderCardProps) {
  const getOrderPriority = () => {
    const orderTime = new Date(order.created_at);
    const now = new Date();
    const minutesAgo = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));
    
    if (minutesAgo > 15) return 'high';
    if (minutesAgo > 10) return 'medium';
    return 'low';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-lupizza-red-500';
      case 'medium': return 'bg-yellow-500';
      default: return 'bg-lupizza-green-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500';
      case 'processing': return 'bg-blue-500';
      case 'ready': return 'bg-lupizza-green-500';
      case 'completed': return 'bg-slate-500';
      default: return 'bg-slate-400';
    }
  };

  const priority = getOrderPriority();
  const orderTime = new Date(order.created_at);
  const timeAgo = Math.floor((new Date().getTime() - orderTime.getTime()) / (1000 * 60));

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-300 hover:shadow-lg",
      className
    )}>
      {/* Priority indicator */}
      <div className={`absolute top-0 left-0 w-full h-1 ${getPriorityColor(priority)}`}></div>
      
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex flex-col">
              <span className="font-semibold text-slate-900">
                Order #{order.id.slice(-6).toUpperCase()}
              </span>
              <div className="flex items-center space-x-2 text-sm text-slate-500">
                <Clock className="h-3 w-3" />
                <span>{timeAgo}m ago</span>
              </div>
            </div>
          </div>
          <Badge className={`${getStatusColor(order.status)} text-white border-0`}>
            {order.status}
          </Badge>
        </div>

        <div className="flex items-center justify-between text-sm text-slate-600">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <MapPin className="h-3 w-3" />
              <span className="capitalize">{order.order_type}</span>
            </div>
            {order.table_number && (
              <div className="flex items-center space-x-1">
                <User className="h-3 w-3" />
                <span>Table {order.table_number}</span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-1 font-medium">
            <DollarSign className="h-3 w-3" />
            <span>Rp {order.total.toLocaleString('id-ID')}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Order Items */}
        <div className="space-y-2">
          {order.order_items?.map((item) => (
            <div key={item.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
              <div className="flex-1">
                <p className="font-medium text-slate-900">{item.menu?.name}</p>
                <p className="text-sm text-slate-500">Qty: {item.quantity}</p>
                {item.notes && (
                  <p className="text-xs text-slate-400 mt-1">Note: {item.notes}</p>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Badge 
                  variant={item.status === 'served' ? 'default' : 'secondary'}
                  className={item.status === 'served' ? 'bg-lupizza-green-500' : ''}
                >
                  {item.status}
                </Badge>
                {showActions && variant === 'kitchen' && item.status !== 'served' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onItemStatusChange?.(item.id, 'served')}
                    className="text-xs"
                  >
                    Mark Ready
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Order Notes */}
        {order.notes && (
          <div className="p-3 bg-lupizza-cream-50 rounded-lg">
            <p className="text-sm text-slate-600">
              <span className="font-medium">Notes:</span> {order.notes}
            </p>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex space-x-2 pt-2">
            {variant === 'kitchen' && order.status === 'pending' && (
              <Button
                onClick={() => onStatusChange?.(order.id, 'processing')}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
                size="sm"
              >
                Start Cooking
              </Button>
            )}
            {variant === 'kitchen' && order.status === 'processing' && (
              <Button
                onClick={() => onStatusChange?.(order.id, 'ready')}
                className="flex-1 bg-lupizza-green-600 hover:bg-lupizza-green-700"
                size="sm"
              >
                Mark Ready
              </Button>
            )}
            {variant === 'kitchen' && order.status === 'ready' && (
              <Button
                onClick={() => onStatusChange?.(order.id, 'completed')}
                className="flex-1 bg-slate-600 hover:bg-slate-700"
                size="sm"
              >
                Complete Order
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}



