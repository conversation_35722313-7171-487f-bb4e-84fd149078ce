-- =============================================
-- Verification Script for Lupizza POS Setup
-- =============================================

-- 1. Check if all tables exist
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN (
    'branches', 'users', 'categories', 'menu_items', 
    'item_variants', 'promotions', 'orders', 
    'order_items', 'transactions', 'activity_logs'
)
ORDER BY tablename;

-- 2. Check RLS status for all tables
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN (
    'branches', 'users', 'categories', 'menu_items', 
    'item_variants', 'promotions', 'orders', 
    'order_items', 'transactions', 'activity_logs'
)
ORDER BY tablename;

-- 3. Check policies for each table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 4. Check if realtime is enabled for required tables
SELECT 
    schemaname,
    tablename
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime'
AND schemaname = 'public'
ORDER BY tablename;

-- 5. Check sample data
SELECT 'branches' as table_name, count(*) as record_count FROM branches
UNION ALL
SELECT 'categories', count(*) FROM categories
UNION ALL
SELECT 'menu_items', count(*) FROM menu_items
UNION ALL
SELECT 'item_variants', count(*) FROM item_variants
UNION ALL
SELECT 'promotions', count(*) FROM promotions
ORDER BY table_name;

-- 6. Test menu items with categories join
SELECT 
    c.name as category_name,
    COUNT(m.id) as menu_count
FROM categories c
LEFT JOIN menu_items m ON c.id = m.category_id
GROUP BY c.id, c.name
ORDER BY c.sort_order;

-- 7. Test menu items with variants
SELECT 
    m.name as menu_name,
    COUNT(v.id) as variant_count
FROM menu_items m
LEFT JOIN item_variants v ON m.id = v.menu_item_id
GROUP BY m.id, m.name
ORDER BY m.name;

-- 8. Check indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
AND tablename IN (
    'branches', 'users', 'categories', 'menu_items', 
    'item_variants', 'promotions', 'orders', 
    'order_items', 'transactions', 'activity_logs'
)
ORDER BY tablename, indexname;

-- 9. Check triggers
SELECT 
    trigger_schema,
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing,
    action_statement
FROM information_schema.triggers
WHERE trigger_schema = 'public'
AND event_object_table IN (
    'branches', 'users', 'categories', 'menu_items', 
    'item_variants', 'promotions', 'orders', 
    'order_items', 'transactions', 'activity_logs'
)
ORDER BY event_object_table, trigger_name;

-- 10. Test helper functions
SELECT get_user_role() as current_user_role;
SELECT get_user_branch() as current_user_branch;
