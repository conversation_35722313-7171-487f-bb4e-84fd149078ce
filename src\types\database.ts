export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          name: string;
          full_name?: string;
          role: 'admin' | 'cashier' | 'kitchen';
          email: string;
          phone?: string;
          avatar_url?: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          name: string;
          full_name?: string;
          role: 'admin' | 'cashier' | 'kitchen';
          email: string;
          phone?: string;
          avatar_url?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          full_name?: string;
          role?: 'admin' | 'cashier' | 'kitchen';
          email?: string;
          phone?: string;
          avatar_url?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      menus: {
        Row: {
          id: string;
          name: string;
          category: string;
          price: number;
          description: string;
          image_url: string;
          available: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          category: string;
          price: number;
          description?: string;
          image_url?: string;
          available?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          category?: string;
          price?: number;
          description?: string;
          image_url?: string;
          available?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          order_type: 'dine-in' | 'takeaway' | 'gojek' | 'grab';
          table_number: string;
          status: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
          total: number;
          notes: string;
          created_at: string;
          updated_at: string;
          cashier_id: string;
        };
        Insert: {
          id?: string;
          order_type: 'dine-in' | 'takeaway' | 'gojek' | 'grab';
          table_number?: string;
          status?: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
          total: number;
          notes?: string;
          created_at?: string;
          updated_at?: string;
          cashier_id: string;
        };
        Update: {
          id?: string;
          order_type?: 'dine-in' | 'takeaway' | 'gojek' | 'grab';
          table_number?: string;
          status?: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
          total?: number;
          notes?: string;
          created_at?: string;
          updated_at?: string;
          cashier_id?: string;
        };
      };
      order_items: {
        Row: {
          id: string;
          order_id: string;
          menu_id: string;
          quantity: number;
          notes: string;
          status: 'pending' | 'preparing' | 'ready' | 'served';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          menu_id: string;
          quantity: number;
          notes?: string;
          status?: 'pending' | 'preparing' | 'ready' | 'served';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          menu_id?: string;
          quantity?: number;
          notes?: string;
          status?: 'pending' | 'preparing' | 'ready' | 'served';
          created_at?: string;
          updated_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          order_id: string;
          payment_method: 'cash' | 'qris' | 'card' | 'split';
          total_paid: number;
          change_amount: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          payment_method: 'cash' | 'qris' | 'card' | 'split';
          total_paid: number;
          change_amount?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          payment_method?: 'cash' | 'qris' | 'card' | 'split';
          total_paid?: number;
          change_amount?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      activity_logs: {
        Row: {
          id: string;
          user_id: string;
          action: string;
          description: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: string;
          description: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          action?: string;
          description?: string;
          created_at?: string;
        };
      };
    };
  };
}