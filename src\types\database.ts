export interface Database {
  public: {
    Tables: {
      branches: {
        Row: {
          id: string;
          name: string;
          address: string | null;
          phone: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          address?: string | null;
          phone?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string | null;
          phone?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          role: 'admin' | 'cashier' | 'kitchen';
          branch_id: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          role: 'admin' | 'cashier' | 'kitchen';
          branch_id?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          role?: 'admin' | 'cashier' | 'kitchen';
          branch_id?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          sort_order: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          sort_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          sort_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      menu_items: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          category_id: string;
          base_price: number;
          price_grab: number | null;
          price_gojek: number | null;
          image_url: string | null;
          is_available: boolean;
          is_beverage: boolean;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          category_id: string;
          base_price?: number;
          price_grab?: number | null;
          price_gojek?: number | null;
          image_url?: string | null;
          is_available?: boolean;
          is_beverage?: boolean;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          category_id?: string;
          base_price?: number;
          price_grab?: number | null;
          price_gojek?: number | null;
          image_url?: string | null;
          is_available?: boolean;
          is_beverage?: boolean;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      item_variants: {
        Row: {
          id: string;
          menu_item_id: string;
          variant_type: string;
          variant_name: string;
          price_adjustment: number;
          is_default: boolean;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          menu_item_id: string;
          variant_type: string;
          variant_name: string;
          price_adjustment?: number;
          is_default?: boolean;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          menu_item_id?: string;
          variant_type?: string;
          variant_name?: string;
          price_adjustment?: number;
          is_default?: boolean;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      promotions: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          promo_type: 'category' | 'item' | 'total';
          discount_type: 'percentage' | 'fixed';
          discount_value: number;
          target_category_id: string | null;
          target_item_id: string | null;
          min_purchase: number;
          start_date: string;
          end_date: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          promo_type: 'category' | 'item' | 'total';
          discount_type: 'percentage' | 'fixed';
          discount_value: number;
          target_category_id?: string | null;
          target_item_id?: string | null;
          min_purchase?: number;
          start_date: string;
          end_date: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          promo_type?: 'category' | 'item' | 'total';
          discount_type?: 'percentage' | 'fixed';
          discount_value?: number;
          target_category_id?: string | null;
          target_item_id?: string | null;
          min_purchase?: number;
          start_date?: string;
          end_date?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          order_number: string;
          order_date: string;
          order_type: 'dine_in' | 'take_away' | 'delivery_grab' | 'delivery_gojek';
          table_number: string | null;
          customer_name: string | null;
          customer_phone: string | null;
          status: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
          subtotal: number;
          discount_amount: number;
          tax_amount: number;
          total_amount: number;
          notes: string | null;
          cashier_id: string;
          branch_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_number: string;
          order_date?: string;
          order_type: 'dine_in' | 'take_away' | 'delivery_grab' | 'delivery_gojek';
          table_number?: string | null;
          customer_name?: string | null;
          customer_phone?: string | null;
          status?: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
          subtotal?: number;
          discount_amount?: number;
          tax_amount?: number;
          total_amount?: number;
          notes?: string | null;
          cashier_id: string;
          branch_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_number?: string;
          order_date?: string;
          order_type?: 'dine_in' | 'take_away' | 'delivery_grab' | 'delivery_gojek';
          table_number?: string | null;
          customer_name?: string | null;
          customer_phone?: string | null;
          status?: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
          subtotal?: number;
          discount_amount?: number;
          tax_amount?: number;
          total_amount?: number;
          notes?: string | null;
          cashier_id?: string;
          branch_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      order_items: {
        Row: {
          id: string;
          order_id: string;
          menu_item_id: string;
          quantity: number;
          unit_price: number;
          total_price: number;
          notes: string | null;
          variant_selections: any; // JSONB
          is_prepared: boolean;
          prepared_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          menu_item_id: string;
          quantity?: number;
          unit_price: number;
          total_price: number;
          notes?: string | null;
          variant_selections?: any; // JSONB
          is_prepared?: boolean;
          prepared_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          menu_item_id?: string;
          quantity?: number;
          unit_price?: number;
          total_price?: number;
          notes?: string | null;
          variant_selections?: any; // JSONB
          is_prepared?: boolean;
          prepared_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          order_id: string;
          payment_method: 'cash' | 'qris' | 'card' | 'split';
          amount_paid: number;
          change_amount: number;
          payment_details: any; // JSONB
          transaction_date: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          payment_method: 'cash' | 'qris' | 'card' | 'split';
          amount_paid: number;
          change_amount?: number;
          payment_details?: any; // JSONB
          transaction_date?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          payment_method?: 'cash' | 'qris' | 'card' | 'split';
          amount_paid?: number;
          change_amount?: number;
          payment_details?: any; // JSONB
          transaction_date?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      activity_logs: {
        Row: {
          id: string;
          user_id: string;
          action: string;
          description: string | null;
          entity_type: string | null;
          entity_id: string | null;
          metadata: any; // JSONB
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: string;
          description?: string | null;
          entity_type?: string | null;
          entity_id?: string | null;
          metadata?: any; // JSONB
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          action?: string;
          description?: string | null;
          entity_type?: string | null;
          entity_id?: string | null;
          metadata?: any; // JSONB
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
      };
    };
  };
}