import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, ChefHat, Utensils, Coffee } from 'lucide-react';

interface MenuEmptyStateProps {
  onAddMenu: () => void;
  hasFilters?: boolean;
  onClearFilters?: () => void;
}

export function MenuEmptyState({ onAddMenu, hasFilters, onClearFilters }: MenuEmptyStateProps) {
  if (hasFilters) {
    return (
      <Card className="border-dashed border-2 border-slate-300">
        <CardContent className="flex flex-col items-center justify-center py-16">
          <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6">
            <Utensils className="h-12 w-12 text-slate-400" />
          </div>
          <h3 className="text-xl font-semibold text-slate-900 mb-2">
            No menu items found
          </h3>
          <p className="text-slate-600 text-center mb-6 max-w-md">
            No menu items match your current filters. Try adjusting your search criteria or clear the filters.
          </p>
          <div className="flex space-x-3">
            {onClearFilters && (
              <Button variant="outline" onClick={onClearFilters}>
                Clear Filters
              </Button>
            )}
            <Button onClick={onAddMenu} className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Menu Item
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-dashed border-2 border-slate-300">
      <CardContent className="flex flex-col items-center justify-center py-20">
        {/* Animated Icons */}
        <div className="relative mb-8">
          <div className="w-32 h-32 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center">
            <ChefHat className="h-16 w-16 text-green-600" />
          </div>
          <div className="absolute -top-2 -right-2 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center animate-bounce">
            <Coffee className="h-6 w-6 text-orange-600" />
          </div>
          <div className="absolute -bottom-2 -left-2 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center animate-pulse">
            <Utensils className="h-5 w-5 text-red-600" />
          </div>
        </div>

        <h3 className="text-2xl font-bold text-slate-900 mb-3">
          Welcome to Menu Management
        </h3>
        <p className="text-slate-600 text-center mb-8 max-w-lg">
          Start building your restaurant's menu by adding your first delicious item. 
          You can add pizzas, pastas, beverages, and more!
        </p>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <Button onClick={onAddMenu} size="lg" className="bg-green-600 hover:bg-green-700">
            <Plus className="h-5 w-5 mr-2" />
            Add Your First Menu Item
          </Button>
        </div>

        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <ChefHat className="h-6 w-6 text-blue-600" />
            </div>
            <h4 className="font-semibold text-slate-900 mb-1">Easy Management</h4>
            <p className="text-sm text-slate-600">Add, edit, and organize your menu items with ease</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Utensils className="h-6 w-6 text-green-600" />
            </div>
            <h4 className="font-semibold text-slate-900 mb-1">Categories</h4>
            <p className="text-sm text-slate-600">Organize items by pizza, pasta, drinks, and more</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Coffee className="h-6 w-6 text-orange-600" />
            </div>
            <h4 className="font-semibold text-slate-900 mb-1">Real-time Updates</h4>
            <p className="text-sm text-slate-600">Changes reflect immediately across your system</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
