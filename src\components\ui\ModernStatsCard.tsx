"use client";

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface ModernStatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
  className?: string;
  iconColor?: string;
  loading?: boolean;
}

export function ModernStatsCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  className,
  iconColor = "text-green-600",
  loading = false
}: ModernStatsCardProps) {
  return (
    <Card className={cn(
      "overflow-hidden border-0 shadow-sm bg-white/50 backdrop-blur-sm",
      "hover:shadow-md transition-all duration-200 hover:scale-[1.02]",
      className
    )}>
      <CardContent className="p-6">
        {loading ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="w-8 h-8 bg-slate-200 rounded-lg animate-pulse"></div>
              <div className="w-16 h-4 bg-slate-200 rounded animate-pulse"></div>
            </div>
            <div className="w-24 h-8 bg-slate-200 rounded animate-pulse"></div>
            <div className="w-32 h-4 bg-slate-200 rounded animate-pulse"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Header with Icon and Trend */}
            <div className="flex items-center justify-between">
              <div className={cn(
                "w-10 h-10 rounded-xl flex items-center justify-center",
                "bg-gradient-to-br from-green-50 to-green-100/50 border border-green-200/50"
              )}>
                <Icon className={cn("h-5 w-5", iconColor)} />
              </div>
              {trend && (
                <div className={cn(
                  "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium",
                  trend.isPositive 
                    ? "bg-green-50 text-green-700 border border-green-200/50" 
                    : "bg-red-50 text-red-700 border border-red-200/50"
                )}>
                  <svg 
                    className={cn(
                      "w-3 h-3",
                      trend.isPositive ? "rotate-0" : "rotate-180"
                    )} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{Math.abs(trend.value)}%</span>
                </div>
              )}
            </div>

            {/* Main Value */}
            <div className="space-y-1">
              <div className="text-2xl font-bold text-slate-900 tracking-tight">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </div>
              <div className="text-sm font-medium text-slate-600">
                {title}
              </div>
            </div>

            {/* Subtitle or Trend Label */}
            {(subtitle || trend?.label) && (
              <div className="text-xs text-slate-500 font-medium">
                {subtitle || trend?.label}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
