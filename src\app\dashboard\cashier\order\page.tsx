"use client";

import { useState } from 'react';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { MenuGrid } from '@/components/cashier/MenuGrid';
import { CartSidebar } from '@/components/cashier/CartSidebar';
import { OrderForm } from '@/components/cashier/OrderForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useMenu, useCategories } from '@/hooks/useMenu';
import { useCreateOrder } from '@/hooks/useOrders';
import { useUser } from '@/hooks/useUser';
import { CartItem } from '@/types';
import toast from 'react-hot-toast';

export default function NewOrderPage() {
  const { data: menus = [] } = useMenu();
  const { data: categories = [] } = useCategories();
  const { data: user } = useUser();
  const createOrderMutation = useCreateOrder();

  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [orderType, setOrderType] = useState<'dine-in' | 'takeaway' | 'gojek' | 'grab'>('dine-in');
  const [tableNumber, setTableNumber] = useState('');
  const [notes, setNotes] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'qris' | 'card'>('cash');
  const [amountPaid, setAmountPaid] = useState('');

  const filteredMenus = selectedCategory 
    ? menus.filter(menu => menu.category === selectedCategory && menu.available)
    : menus.filter(menu => menu.available);

  const subtotal = cart.reduce((sum, item) => sum + (item.menu.price * item.quantity), 0);
  const tax = subtotal * 0.1;
  const total = subtotal + tax;

  const addToCart = (menu: any) => {
    const existingItem = cart.find(item => item.menu_id === menu.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.menu_id === menu.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { menu_id: menu.id, menu, quantity: 1 }]);
    }
  };

  const updateQuantity = (menuId: string, quantity: number) => {
    if (quantity <= 0) {
      setCart(cart.filter(item => item.menu_id !== menuId));
    } else {
      setCart(cart.map(item =>
        item.menu_id === menuId
          ? { ...item, quantity }
          : item
      ));
    }
  };

  const removeFromCart = (menuId: string) => {
    setCart(cart.filter(item => item.menu_id !== menuId));
  };

  const clearCart = () => {
    setCart([]);
  };

  const handleSubmitOrder = async () => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    if (cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }

    const paid = parseFloat(amountPaid) || 0;
    if (paymentMethod === 'cash' && paid < total) {
      toast.error('Amount paid is less than total');
      return;
    }

    try {
      await createOrderMutation.mutateAsync({
        orderData: {
          order_type: orderType,
          table_number: tableNumber,
          status: 'pending',
          total,
          notes,
          cashier_id: user.id,
        },
        cartItems: cart,
        transactionData: {
          payment_method: paymentMethod,
          total_paid: paid,
          change_amount: paymentMethod === 'cash' ? Math.max(0, paid - total) : 0,
        },
      });

      // Clear form
      setCart([]);
      setTableNumber('');
      setNotes('');
      setAmountPaid('');
      
      toast.success('Order created and sent to kitchen!');
    } catch (error) {
      console.error('Error creating order:', error);
    }
  };

  const printReceipt = () => {
    const receiptContent = `
      <div style="font-family: monospace; width: 300px; margin: 0 auto;">
        <h2 style="text-align: center;">LuPizza</h2>
        <p style="text-align: center;">Receipt</p>
        <hr>
        <p>Order Type: ${orderType}</p>
        ${tableNumber ? `<p>Table: ${tableNumber}</p>` : ''}
        <p>Date: ${new Date().toLocaleString()}</p>
        <hr>
        ${cart.map(item => `
          <div style="display: flex; justify-content: space-between;">
            <span>${item.menu.name} x${item.quantity}</span>
            <span>Rp ${(item.menu.price * item.quantity).toLocaleString('id-ID')}</span>
          </div>
        `).join('')}
        <hr>
        <div style="display: flex; justify-content: space-between;">
          <span>Subtotal:</span>
          <span>Rp ${subtotal.toLocaleString('id-ID')}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Tax (10%):</span>
          <span>Rp ${tax.toLocaleString('id-ID')}</span>
        </div>
        <div style="display: flex; justify-content: space-between; font-weight: bold;">
          <span>Total:</span>
          <span>Rp ${total.toLocaleString('id-ID')}</span>
        </div>
        <hr>
        <p style="text-align: center;">Thank you for your order!</p>
      </div>
    `;
    
    const printWindow = window.open('', '', 'height=600,width=400');
    printWindow?.document.write(receiptContent);
    printWindow?.document.close();
    printWindow?.print();
  };

  return (
    <DashboardLayout requiredRole="cashier">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Menu Section */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Menu</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <Label>Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <MenuGrid 
                menus={filteredMenus}
                onAddToCart={addToCart}
              />
            </CardContent>
          </Card>
        </div>

        {/* Cart & Order Form Section */}
        <div className="space-y-6">
          <CartSidebar
            cart={cart}
            onUpdateQuantity={updateQuantity}
            onRemoveItem={removeFromCart}
            onClearCart={clearCart}
          />

          <OrderForm
            cart={cart}
            orderType={orderType}
            tableNumber={tableNumber}
            notes={notes}
            paymentMethod={paymentMethod}
            amountPaid={amountPaid}
            onOrderTypeChange={setOrderType}
            onTableNumberChange={setTableNumber}
            onNotesChange={setNotes}
            onPaymentMethodChange={setPaymentMethod}
            onAmountPaidChange={setAmountPaid}
            onSubmitOrder={handleSubmitOrder}
            onPrintReceipt={printReceipt}
            isSubmitting={createOrderMutation.status === 'pending'}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
