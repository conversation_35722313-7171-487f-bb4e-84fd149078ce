'use client';

import { Button } from '@/components/ui/button';
// import { MaterialDateRangePicker } from '@/components/ui/MaterialDateRangePicker';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar, Filter, Download } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

interface DashboardFiltersProps {
  // Date Range Props
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
  selectedPeriod: string;
  onPeriodSelect: (period: string) => void;

  // Filter Dialog Props
  isFilterOpen: boolean;
  onFilterOpenChange: (open: boolean) => void;

  // Export Props
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  isExporting?: boolean;

  // Customization
  showExport?: boolean;
  showCustomFilter?: boolean;
  className?: string;
}

export function DashboardFilters({
  dateRange,
  onDateRangeChange,
  selectedPeriod,
  onPeriodSelect,
  isFilterOpen,
  onFilterOpenChange,
  onExport,
  isExporting = false,
  showExport = true,
  showCustomFilter = true,
  className,
}: DashboardFiltersProps) {
  const getPeriodLabel = () => {
    if (selectedPeriod === 'custom' && dateRange.from && dateRange.to) {
      const fromDate = format(dateRange.from, 'MMM dd');
      const toDate = format(dateRange.to, 'MMM dd');
      return `${fromDate} - ${toDate}`;
    }

    switch (selectedPeriod) {
      case '7days':
        return 'Last 7 days';
      case '30days':
        return 'Last 30 days';
      case '90days':
        return 'Last 90 days';
      case 'year':
        return 'This year';
      case 'custom':
        return 'Custom range';
      default:
        return 'Last 7 days';
    }
  };

  const handleApplyFilter = () => {
    onPeriodSelect('custom');
    onFilterOpenChange(false);
    toast.success('Filter applied successfully');
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Date Range Picker */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="border-slate-200 hover:bg-slate-50 text-slate-700 font-medium"
          >
            <Calendar className="h-3.5 w-3.5 mr-1.5" />
            {getPeriodLabel()}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={() => onPeriodSelect('7days')}>
            Last 7 days
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPeriodSelect('30days')}>
            Last 30 days
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPeriodSelect('90days')}>
            Last 90 days
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onPeriodSelect('year')}>
            This year
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom Filter Dialog */}
      {showCustomFilter && (
        <Popover open={isFilterOpen} onOpenChange={onFilterOpenChange}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="border-slate-200 hover:bg-slate-50 text-slate-700 font-medium"
            >
              <Filter className="h-3.5 w-3.5 mr-1.5" />
              Filter
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="w-auto p-0 bg-transparent border-0 shadow-none"
            align="end"
            side="bottom"
            sideOffset={8}
          >
            <div className="p-4 bg-white rounded-lg shadow-lg border">
              <p className="text-sm text-slate-600">
                Date range picker will be implemented here
              </p>
            </div>
          </PopoverContent>
        </Popover>
      )}

      {/* Export Dropdown */}
      {showExport && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="bg-green-600 hover:bg-green-700 text-white"
              disabled={isExporting}
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? 'Exporting...' : 'Export Report'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => onExport?.('csv')}>
              Export as CSV
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onExport?.('excel')}>
              Export as Excel
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onExport?.('pdf')}>
              Export as PDF
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
