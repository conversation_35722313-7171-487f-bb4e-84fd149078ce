"use client";

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface DashboardHeaderProps {
  title: string;
  subtitle?: string;
  children?: ReactNode;
  className?: string;
}

export function DashboardHeader({ 
  title, 
  subtitle, 
  children, 
  className 
}: DashboardHeaderProps) {
  return (
    <div className={cn(
      "flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",
      className
    )}>
      <div className="space-y-1">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
          {title}
        </h1>
        {subtitle && (
          <p className="text-slate-600 mt-1 font-medium">
            {subtitle}
          </p>
        )}
      </div>
      {children && (
        <div className="flex items-center space-x-3">
          {children}
        </div>
      )}
    </div>
  );
}
