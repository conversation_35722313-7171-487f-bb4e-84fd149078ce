"use client";

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Calculator, Percent, Save } from 'lucide-react';
import toast from 'react-hot-toast';

const taxSchema = z.object({
  tax_rate: z.number().min(0).max(100),
  service_charge: z.number().min(0).max(100),
  tax_enabled: z.boolean(),
  service_charge_enabled: z.boolean(),
  tax_inclusive: z.boolean(),
});

type TaxFormData = z.infer<typeof taxSchema>;

export function TaxSettings() {
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<TaxFormData>({
    resolver: zodResolver(taxSchema),
    defaultValues: {
      tax_rate: 10,
      service_charge: 5,
      tax_enabled: true,
      service_charge_enabled: true,
      tax_inclusive: false,
    }
  });

  const onSubmit = async (data: TaxFormData) => {
    setIsLoading(true);
    try {
      // Save tax settings
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Tax settings updated successfully');
    } catch (error) {
      toast.error('Failed to update tax settings');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateExample = () => {
    const baseAmount = 100000;
    const taxRate = watch('tax_rate') || 0;
    const serviceRate = watch('service_charge') || 0;
    const taxEnabled = watch('tax_enabled');
    const serviceEnabled = watch('service_charge_enabled');
    const taxInclusive = watch('tax_inclusive');

    let subtotal = baseAmount;
    let tax = 0;
    let service = 0;
    let total = baseAmount;

    if (serviceEnabled) {
      service = (baseAmount * serviceRate) / 100;
    }

    if (taxEnabled) {
      if (taxInclusive) {
        tax = (baseAmount * taxRate) / (100 + taxRate);
        subtotal = baseAmount - tax;
      } else {
        tax = ((baseAmount + service) * taxRate) / 100;
      }
    }

    if (!taxInclusive) {
      total = baseAmount + service + tax;
    }

    return { subtotal, service, tax, total };
  };

  const example = calculateExample();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calculator className="h-5 w-5 text-lupizza-green-600" />
            <span>Tax & Service Charge Settings</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Tax Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base font-medium">Tax (PPN)</Label>
                  <p className="text-sm text-slate-600">Enable and configure tax rate</p>
                </div>
                <Switch
                  checked={watch('tax_enabled')}
                  onCheckedChange={(checked) => setValue('tax_enabled', checked)}
                />
              </div>

              {watch('tax_enabled') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-4 border-l-2 border-lupizza-green-200">
                  <div className="space-y-2">
                    <Label htmlFor="tax_rate">Tax Rate (%)</Label>
                    <div className="relative">
                      <Input
                        id="tax_rate"
                        type="number"
                        step="0.1"
                        min="0"
                        max="100"
                        {...register('tax_rate', { valueAsNumber: true })}
                        className="pr-8"
                      />
                      <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    </div>
                    {errors.tax_rate && (
                      <p className="text-sm text-red-600">{errors.tax_rate.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={watch('tax_inclusive')}
                        onCheckedChange={(checked) => setValue('tax_inclusive', checked)}
                      />
                      <Label>Tax Inclusive</Label>
                    </div>
                    <p className="text-xs text-slate-600">
                      {watch('tax_inclusive') 
                        ? 'Tax is included in menu prices' 
                        : 'Tax is added to menu prices'
                      }
                    </p>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Service Charge Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base font-medium">Service Charge</Label>
                  <p className="text-sm text-slate-600">Enable and configure service charge</p>
                </div>
                <Switch
                  checked={watch('service_charge_enabled')}
                  onCheckedChange={(checked) => setValue('service_charge_enabled', checked)}
                />
              </div>

              {watch('service_charge_enabled') && (
                <div className="pl-4 border-l-2 border-lupizza-green-200">
                  <div className="space-y-2 max-w-md">
                    <Label htmlFor="service_charge">Service Charge (%)</Label>
                    <div className="relative">
                      <Input
                        id="service_charge"
                        type="number"
                        step="0.1"
                        min="0"
                        max="100"
                        {...register('service_charge', { valueAsNumber: true })}
                        className="pr-8"
                      />
                      <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    </div>
                    {errors.service_charge && (
                      <p className="text-sm text-red-600">{errors.service_charge.message}</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={!isDirty || isLoading}
                className="bg-lupizza-green-600 hover:bg-lupizza-green-700"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Settings
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Calculation Example */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Calculation Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-slate-50 rounded-lg p-4 space-y-3">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span className="font-medium">
                {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(example.subtotal)}
              </span>
            </div>
            {watch('service_charge_enabled') && (
              <div className="flex justify-between">
                <span>Service Charge ({watch('service_charge')}%):</span>
                <span className="font-medium">
                  {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(example.service)}
                </span>
              </div>
            )}
            {watch('tax_enabled') && (
              <div className="flex justify-between">
                <span>Tax ({watch('tax_rate')}%):</span>
                <span className="font-medium">
                  {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(example.tax)}
                </span>
              </div>
            )}
            <Separator />
            <div className="flex justify-between text-lg font-semibold">
              <span>Total:</span>
              <span className="text-lupizza-green-600">
                {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(example.total)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}