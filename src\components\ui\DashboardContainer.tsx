"use client";

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface DashboardContainerProps {
  children: ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg';
}

export function DashboardContainer({ 
  children, 
  className,
  spacing = 'lg'
}: DashboardContainerProps) {
  const spacingClasses = {
    sm: 'space-y-4',
    md: 'space-y-6', 
    lg: 'space-y-8'
  };

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-slate-50 to-white",
      spacingClasses[spacing],
      className
    )}>
      {children}
    </div>
  );
}

interface DashboardSectionProps {
  children: ReactNode;
  className?: string;
  title?: string;
  subtitle?: string;
}

export function DashboardSection({ 
  children, 
  className,
  title,
  subtitle 
}: DashboardSectionProps) {
  return (
    <section className={cn("space-y-4", className)}>
      {(title || subtitle) && (
        <div className="space-y-1">
          {title && (
            <h2 className="text-xl font-semibold text-slate-900">{title}</h2>
          )}
          {subtitle && (
            <p className="text-slate-600">{subtitle}</p>
          )}
        </div>
      )}
      {children}
    </section>
  );
}

interface DashboardGridProps {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
  gap?: 'sm' | 'md' | 'lg';
}

export function DashboardGrid({ 
  children, 
  columns = 3,
  className,
  gap = 'md'
}: DashboardGridProps) {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 lg:grid-cols-2',
    3: 'grid-cols-1 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  };

  return (
    <div className={cn(
      "grid",
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
}
