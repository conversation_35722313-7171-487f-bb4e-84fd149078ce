import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus } from 'lucide-react';
import { MenuItem } from '@/types';
import { cn } from '@/lib/utils';

interface MenuGridProps {
  menus: MenuItem[];
  onAddToCart: (menu: MenuItem) => void;
  className?: string;
}

export function MenuGrid({ menus, onAddToCart, className }: MenuGridProps) {
  if (menus.length === 0) {
    return (
      <div className="text-center py-8 text-slate-500">
        <p>No menu items available</p>
      </div>
    );
  }

  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-4', className)}>
      {menus.map((menu) => (
        <Card key={menu.id} className="hover:shadow-md transition-shadow group">
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1">
                <h3 className="font-semibold text-slate-900 group-hover:text-lupizza-green-600 transition-colors">
                  {menu.name}
                </h3>
                <p className="text-sm text-slate-600 mt-1 line-clamp-2">
                  {menu.description}
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    {menu.categories?.name || 'No Category'}
                  </Badge>
                  {!menu.is_available && (
                    <Badge variant="destructive" className="text-xs">
                      Unavailable
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-right ml-4">
                <p className="font-bold text-lg text-slate-900">
                  Rp {menu.base_price.toLocaleString('id-ID')}
                </p>
                <Button
                  onClick={() => onAddToCart(menu)}
                  disabled={!menu.is_available}
                  size="sm"
                  className="mt-2 bg-lupizza-green-600 hover:bg-lupizza-green-700 disabled:opacity-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
