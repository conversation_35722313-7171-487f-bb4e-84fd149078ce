import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChartCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  showMenu?: boolean;
  onMenuClick?: () => void;
}

export function ChartCard({ 
  title, 
  children, 
  className,
  showMenu = true,
  onMenuClick 
}: ChartCardProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="bg-gradient-to-r from-white to-slate-50/50">
        <div className="flex items-center justify-between">
          <CardTitle className="text-slate-900">{title}</CardTitle>
          {showMenu && (
            <Button variant="ghost" size="sm" onClick={onMenuClick}>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {children}
      </CardContent>
    </Card>
  );
}