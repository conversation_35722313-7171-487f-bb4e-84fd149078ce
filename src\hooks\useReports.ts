"use client";

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Order, OrderWithItems } from '@/types';

interface ReportData {
  sales?: {
    totalRevenue: number;
    totalOrders: number;
    avgOrderValue: number;
    topItems: Array<{
      name: string;
      quantity: number;
      revenue: number;
    }>;
    dailyBreakdown: Array<{
      date: string;
      revenue: number;
      orders: number;
    }>;
  };
  menu?: {
    totalItems: number;
    activeItems: number;
    topPerformers: Array<{
      name: string;
      category: string;
      totalSold: number;
      revenue: number;
    }>;
    categoryBreakdown: Array<{
      category: string;
      itemCount: number;
      revenue: number;
    }>;
  };
  staff?: {
    totalStaff: number;
    activeStaff: number;
    roleBreakdown: Array<{
      role: string;
      count: number;
    }>;
    performance: Array<{
      name: string;
      role: string;
      ordersProcessed: number;
      revenue: number;
    }>;
  };
}

const reportService = {
  async getSalesReport(dateRange: { from: Date; to: Date }): Promise<ReportData['sales']> {
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          menu:menus (name, category)
        )
      `)
      .gte('created_at', dateRange.from.toISOString())
      .lte('created_at', dateRange.to.toISOString())
      .eq('status', 'completed');

    if (error) throw error;

    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = orders.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate top items
    const itemStats = new Map();
    orders.forEach(order => {
      order.order_items?.forEach(item => {
        const key = item.menu?.name || 'Unknown';
        const existing = itemStats.get(key) || { quantity: 0, revenue: 0 };
        itemStats.set(key, {
          quantity: existing.quantity + item.quantity,
          revenue: existing.revenue + (item.price * item.quantity)
        });
      });
    });

    const topItems = Array.from(itemStats.entries())
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Daily breakdown
    const dailyStats = new Map();
    orders.forEach(order => {
      const date = new Date(order.created_at).toISOString().split('T')[0];
      const existing = dailyStats.get(date) || { revenue: 0, orders: 0 };
      dailyStats.set(date, {
        revenue: existing.revenue + order.total,
        orders: existing.orders + 1
      });
    });

    const dailyBreakdown = Array.from(dailyStats.entries())
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalRevenue,
      totalOrders,
      avgOrderValue,
      topItems,
      dailyBreakdown
    };
  },

  async getMenuReport(): Promise<ReportData['menu']> {
    const { data: menus, error } = await supabase
      .from('menus')
      .select('*');

    if (error) throw error;

    const totalItems = menus.length;
    const activeItems = menus.filter(menu => menu.available).length;

    // Get order items to calculate performance
    const { data: orderItems, error: orderError } = await supabase
      .from('order_items')
      .select(`
        *,
        menu:menus (name, category),
        order:orders!inner (status)
      `)
      .eq('order.status', 'completed');

    if (orderError) throw orderError;

    // Calculate top performers
    const menuStats = new Map();
    orderItems.forEach(item => {
      const key = item.menu?.name || 'Unknown';
      const existing = menuStats.get(key) || { 
        category: item.menu?.category || 'Unknown',
        totalSold: 0, 
        revenue: 0 
      };
      menuStats.set(key, {
        ...existing,
        totalSold: existing.totalSold + item.quantity,
        revenue: existing.revenue + (item.price * item.quantity)
      });
    });

    const topPerformers = Array.from(menuStats.entries())
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Category breakdown
    const categoryStats = new Map();
    menus.forEach(menu => {
      const existing = categoryStats.get(menu.category) || { itemCount: 0, revenue: 0 };
      const menuRevenue = menuStats.get(menu.name)?.revenue || 0;
      categoryStats.set(menu.category, {
        itemCount: existing.itemCount + 1,
        revenue: existing.revenue + menuRevenue
      });
    });

    const categoryBreakdown = Array.from(categoryStats.entries())
      .map(([category, stats]) => ({ category, ...stats }));

    return {
      totalItems,
      activeItems,
      topPerformers,
      categoryBreakdown
    };
  },

  async getStaffReport(): Promise<ReportData['staff']> {
    const { data: users, error } = await supabase
      .from('users')
      .select('*');

    if (error) throw error;

    const totalStaff = users.length;
    const activeStaff = users.filter(user => user.is_active !== false).length;

    // Role breakdown
    const roleStats = new Map();
    users.forEach(user => {
      const existing = roleStats.get(user.role) || 0;
      roleStats.set(user.role, existing + 1);
    });

    const roleBreakdown = Array.from(roleStats.entries())
      .map(([role, count]) => ({ role, count }));

    // Performance (mock data for now - would need order tracking by cashier)
    const performance = users
      .filter(user => user.role === 'cashier')
      .map(user => ({
        name: user.full_name || user.email,
        role: user.role,
        ordersProcessed: Math.floor(Math.random() * 100), // Mock data
        revenue: Math.floor(Math.random() * 1000000) // Mock data
      }));

    return {
      totalStaff,
      activeStaff,
      roleBreakdown,
      performance
    };
  }
};

export const useReports = (
  reportType: 'sales' | 'menu' | 'staff',
  dateRange: { from: Date; to: Date }
) => {
  return useQuery({
    queryKey: ['reports', reportType, dateRange],
    queryFn: async () => {
      switch (reportType) {
        case 'sales':
          return { sales: await reportService.getSalesReport(dateRange) };
        case 'menu':
          return { menu: await reportService.getMenuReport() };
        case 'staff':
          return { staff: await reportService.getStaffReport() };
        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
