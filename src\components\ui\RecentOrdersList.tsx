"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Eye } from 'lucide-react';
import { Order } from '@/types';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface RecentOrdersListProps {
  orders: Order[];
  onViewAll?: () => void;
  maxItems?: number;
  title?: string;
  className?: string;
  showViewAll?: boolean;
}

export function RecentOrdersList({
  orders,
  onViewAll,
  maxItems = 5,
  title = "Recent Orders",
  className,
  showViewAll = true
}: RecentOrdersListProps) {
  
  const displayOrders = orders.slice(0, maxItems);
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700';
      case 'processing':
        return 'bg-blue-100 text-blue-700';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700';
      case 'cancelled':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (orders.length === 0) {
    return (
      <Card className={cn("overflow-hidden", className)}>
        <CardHeader className="bg-gradient-to-r from-white to-slate-50/50">
          <CardTitle className="text-slate-900">{title}</CardTitle>
        </CardHeader>
        <CardContent className="p-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-900 mb-2">No orders found</h3>
            <p className="text-slate-600">Orders will appear here when they are placed.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="bg-gradient-to-r from-white to-slate-50/50 border-b border-slate-100/50">
        <div className="flex items-center justify-between">
          <CardTitle className="text-slate-900">{title}</CardTitle>
          {showViewAll && onViewAll && (
            <Button variant="outline" size="sm" onClick={onViewAll}>
              <Eye className="h-4 w-4 mr-1" />
              View All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="divide-y divide-slate-100">
          {displayOrders.map((order, index) => (
            <div 
              key={order.id} 
              className="p-6 hover:bg-slate-50/50 transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                    <span className="text-sm font-semibold text-green-600">
                      #{order.id.slice(-3)}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-slate-900">
                      Order #{order.id.slice(-6)}
                    </p>
                    <div className="flex items-center space-x-2 text-sm text-slate-500 mt-1">
                      <span>{order.order_type}</span>
                      {order.table_number && (
                        <>
                          <span>•</span>
                          <span>Table {order.table_number}</span>
                        </>
                      )}
                      <span>•</span>
                      <span>{format(new Date(order.created_at), 'MMM dd, HH:mm')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-slate-900">
                    Rp {order.total.toLocaleString('id-ID')}
                  </p>
                  <div className="flex items-center justify-end mt-1">
                    <div className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      getStatusColor(order.status)
                    )}>
                      {order.status}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Footer with count */}
        {orders.length > maxItems && (
          <div className="px-6 py-3 bg-slate-50/50 border-t border-slate-100">
            <p className="text-sm text-slate-600 text-center">
              Showing {maxItems} of {orders.length} orders
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
