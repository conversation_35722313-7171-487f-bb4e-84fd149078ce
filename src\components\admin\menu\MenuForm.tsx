'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  useCreateMenuItem,
  useUpdateMenuItem,
  useCategories,
} from '@/hooks/useMenu';
import { MenuItem } from '@/types';
import { Upload, X } from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';
import { storageService } from '@/services/storageService';

const menuSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  base_price: z.number().min(0, 'Price must be positive'),
  category_id: z.string().min(1, 'Category is required'),
  is_available: z.boolean(),
});

type MenuFormData = z.infer<typeof menuSchema>;

interface MenuFormProps {
  menu?: MenuItem | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function MenuForm({ menu, onSuccess, onCancel }: MenuFormProps) {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [formKey, setFormKey] = useState<string>('new');

  const createMenuItem = useCreateMenuItem();
  const updateMenuItem = useUpdateMenuItem();
  const { data: categories = [] } = useCategories();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<MenuFormData>({
    resolver: zodResolver(menuSchema),
    defaultValues: {
      name: '',
      description: '',
      base_price: 0,
      category_id: '',
      is_available: true,
    },
  });

  // Reset form when menu prop changes
  useEffect(() => {
    if (menu) {
      // Edit mode - populate form with menu data
      setFormKey(`edit-${menu.id}`);
      reset({
        name: menu.name,
        description: menu.description || '',
        base_price: menu.base_price,
        category_id: menu.category_id,
        is_available: menu.is_available,
      });
      setImagePreview(menu.image_url || '');
      setImageFile(null);
    } else {
      // Add mode - reset form to empty values
      setFormKey('new');
      reset({
        name: '',
        description: '',
        base_price: 0,
        category_id: '',
        is_available: true,
      });
      setImagePreview('');
      setImageFile(null);
    }
  }, [menu, reset]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate image file
      const validation = storageService.validateImageFile(file);
      if (!validation.isValid) {
        toast.error(validation.error || 'Invalid image file');
        return;
      }

      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setImageFile(null);
    setImagePreview('');
  };

  const onSubmit = async (data: MenuFormData) => {
    try {
      if (menu) {
        await updateMenuItem.mutateAsync({
          id: menu.id,
          updates: {
            name: data.name,
            description: data.description || '',
            base_price: data.base_price,
            category_id: data.category_id,
            is_available: data.is_available,
            image_file: imageFile,
          },
        });
        toast.success('Menu updated successfully');
      } else {
        await createMenuItem.mutateAsync({
          name: data.name,
          description: data.description || '',
          base_price: data.base_price,
          category_id: data.category_id,
          is_available: data.is_available,
          image_url: '',
          image_file: imageFile,
        });
        toast.success('Menu created successfully');
      }
      onSuccess();
    } catch (error) {
      toast.error('Failed to save menu item');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Image Upload */}
      <div className="space-y-2">
        <Label>Image</Label>
        <div className="flex items-center space-x-4">
          {imagePreview ? (
            <div className="relative w-24 h-24 rounded-lg overflow-hidden">
              <Image
                src={imagePreview}
                alt="Preview"
                fill
                className="object-cover"
              />
              <button
                type="button"
                onClick={removeImage}
                className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ) : (
            <div className="w-24 h-24 border-2 border-dashed border-slate-300 rounded-lg flex items-center justify-center">
              <Upload className="h-6 w-6 text-slate-400" />
            </div>
          )}
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="hidden"
              id="image-upload"
            />
            <Label htmlFor="image-upload" className="cursor-pointer">
              <Button type="button" variant="outline" asChild>
                <span>Choose Image</span>
              </Button>
            </Label>
          </div>
        </div>
      </div>

      {/* Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name *</Label>
          <Input id="name" {...register('name')} placeholder="Menu item name" />
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category *</Label>
          <Select
            key={`category-${formKey}`}
            value={watch('category_id') || ''}
            onValueChange={(value) => setValue('category_id', value)}
          >
            <SelectTrigger className="bg-white border-slate-200">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent className="bg-white border border-slate-200 shadow-lg">
              {categories.map((category) => (
                <SelectItem
                  key={category.id}
                  value={category.id}
                  className="hover:bg-slate-50"
                >
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.category_id && (
            <p className="text-sm text-red-600">{errors.category_id.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          {...register('description')}
          placeholder="Describe your menu item..."
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="base_price">Price *</Label>
          <Input
            id="base_price"
            type="number"
            step="0.01"
            {...register('base_price', { valueAsNumber: true })}
            placeholder="0.00"
          />
          {errors.base_price && (
            <p className="text-sm text-red-600">{errors.base_price.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Availability</Label>
          <div className="flex items-center space-x-3 pt-2">
            <Switch
              checked={watch('is_available')}
              onCheckedChange={(checked) => setValue('is_available', checked)}
              className=" border-2 border-slate-400"
            />
            <span
              className={`text-sm font-medium ${
                watch('is_available') ? 'text-green-600' : 'text-slate-500'
              }`}
            >
              {watch('is_available') ? 'Available' : 'Unavailable'}
            </span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-green-600 hover:bg-green-700"
        >
          {isSubmitting ? 'Saving...' : menu ? 'Update' : 'Create'}
        </Button>
      </div>
    </form>
  );
}
