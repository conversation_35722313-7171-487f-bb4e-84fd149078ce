import { Card, CardContent } from '@/components/ui/card';
import { OrderWithItems } from '@/types';
import { Bell, ChefHat, Clock, CheckCircle } from 'lucide-react';

interface KitchenStatsProps {
  pendingOrders: OrderWithItems[];
  processingOrders: OrderWithItems[];
  readyOrders: OrderWithItems[];
  completedOrders: OrderWithItems[];
}

export function KitchenStats({ 
  pendingOrders, 
  processingOrders, 
  readyOrders, 
  completedOrders 
}: KitchenStatsProps) {
  const stats = [
    {
      title: 'Pending',
      count: pendingOrders.length,
      icon: Bell,
      color: 'from-lupizza-red-50 to-lupizza-red-100 border-lupizza-red-200',
      iconColor: 'text-lupizza-red-500',
      textColor: 'text-lupizza-red-600'
    },
    {
      title: 'Processing',
      count: processingOrders.length,
      icon: ChefHat,
      color: 'from-blue-50 to-blue-100 border-blue-200',
      iconColor: 'text-blue-500',
      textColor: 'text-blue-600'
    },
    {
      title: 'Ready',
      count: readyOrders.length,
      icon: Clock,
      color: 'from-lupizza-green-50 to-lupizza-green-100 border-lupizza-green-200',
      iconColor: 'text-lupizza-green-500',
      textColor: 'text-lupizza-green-600'
    },
    {
      title: 'Completed',
      count: completedOrders.length,
      icon: CheckCircle,
      color: 'from-slate-50 to-slate-100 border-slate-200',
      iconColor: 'text-slate-500',
      textColor: 'text-slate-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {stats.map((stat) => {
        const Icon = stat.icon;
        return (
          <Card key={stat.title} className={`bg-gradient-to-r ${stat.color}`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm font-medium ${stat.textColor}`}>{stat.title}</p>
                  <p className={`text-2xl font-bold ${stat.textColor.replace('600', '700')}`}>
                    {stat.count}
                  </p>
                </div>
                <Icon className={`h-8 w-8 ${stat.iconColor}`} />
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}