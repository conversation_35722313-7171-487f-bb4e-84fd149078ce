import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { CartItem } from '@/types';
import { Printer, CreditCard, Smartphone, Banknote } from 'lucide-react';
import { useState } from 'react';

interface OrderFormProps {
  cart: CartItem[];
  orderType: 'dine-in' | 'takeaway' | 'gojek' | 'grab';
  tableNumber: string;
  notes: string;
  paymentMethod: 'cash' | 'qris' | 'card';
  amountPaid: string;
  onOrderTypeChange: (type: 'dine-in' | 'takeaway' | 'gojek' | 'grab') => void;
  onTableNumberChange: (table: string) => void;
  onNotesChange: (notes: string) => void;
  onPaymentMethodChange: (method: 'cash' | 'qris' | 'card') => void;
  onAmountPaidChange: (amount: string) => void;
  onSubmitOrder: () => void;
  onPrintReceipt: () => void;
  isSubmitting?: boolean;
}

export function OrderForm({
  cart,
  orderType,
  tableNumber,
  notes,
  paymentMethod,
  amountPaid,
  onOrderTypeChange,
  onTableNumberChange,
  onNotesChange,
  onPaymentMethodChange,
  onAmountPaidChange,
  onSubmitOrder,
  onPrintReceipt,
  isSubmitting = false
}: OrderFormProps) {
  const subtotal = cart.reduce((sum, item) => sum + (item.menu.price * item.quantity), 0);
  const tax = subtotal * 0.1;
  const total = subtotal + tax;
  const change = paymentMethod === 'cash' ? Math.max(0, parseFloat(amountPaid) - total) : 0;

  const paymentIcons = {
    cash: Banknote,
    qris: Smartphone,
    card: CreditCard
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Order Type */}
        <div>
          <Label className="text-sm font-medium">Order Type</Label>
          <RadioGroup 
            value={orderType} 
            onValueChange={onOrderTypeChange}
            className="grid grid-cols-2 gap-2 mt-2"
          >
            {[
              { value: 'dine-in', label: 'Dine In' },
              { value: 'takeaway', label: 'Takeaway' },
              { value: 'gojek', label: 'GoJek' },
              { value: 'grab', label: 'Grab' }
            ].map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.value} />
                <Label htmlFor={option.value} className="text-sm cursor-pointer">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Table Number */}
        {orderType === 'dine-in' && (
          <div>
            <Label htmlFor="table">Table Number</Label>
            <Input
              id="table"
              value={tableNumber}
              onChange={(e) => onTableNumberChange(e.target.value)}
              placeholder="Enter table number"
            />
          </div>
        )}

        {/* Notes */}
        <div>
          <Label htmlFor="notes">Special Notes</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => onNotesChange(e.target.value)}
            placeholder="Any special requests..."
            rows={3}
          />
        </div>

        {/* Payment Method */}
        {cart.length > 0 && (
          <>
            <div>
              <Label className="text-sm font-medium">Payment Method</Label>
              <RadioGroup 
                value={paymentMethod} 
                onValueChange={onPaymentMethodChange}
                className="grid grid-cols-3 gap-2 mt-2"
              >
                {[
                  { value: 'cash', label: 'Cash', icon: Banknote },
                  { value: 'qris', label: 'QRIS', icon: Smartphone },
                  { value: 'card', label: 'Card', icon: CreditCard }
                ].map(option => {
                  const Icon = option.icon;
                  return (
                    <div key={option.value} className="flex items-center space-x-2 p-2 border rounded-lg">
                      <RadioGroupItem value={option.value} id={option.value} />
                      <Label htmlFor={option.value} className="flex items-center space-x-2 cursor-pointer">
                        <Icon className="h-4 w-4" />
                        <span className="text-sm">{option.label}</span>
                      </Label>
                    </div>
                  );
                })}
              </RadioGroup>
            </div>

            {/* Amount Paid (Cash only) */}
            {paymentMethod === 'cash' && (
              <div>
                <Label htmlFor="amount">Amount Paid</Label>
                <Input
                  id="amount"
                  type="number"
                  value={amountPaid}
                  onChange={(e) => onAmountPaidChange(e.target.value)}
                  placeholder="0"
                />
                {change > 0 && (
                  <p className="text-sm text-lupizza-green-600 mt-1">
                    Change: Rp {change.toLocaleString('id-ID')}
                  </p>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-2 pt-4">
              <Button
                onClick={onSubmitOrder}
                disabled={isSubmitting || cart.length === 0}
                className="flex-1 bg-lupizza-green-600 hover:bg-lupizza-green-700"
              >
                {isSubmitting ? 'Creating...' : 'Create Order'}
              </Button>
              <Button variant="outline" onClick={onPrintReceipt}>
                <Printer className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}