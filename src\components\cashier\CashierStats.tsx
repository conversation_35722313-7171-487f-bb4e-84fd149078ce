import { StatCard } from '@/components/ui/StatCard';
import { DollarSign, ShoppingCart, Clock, TrendingUp } from 'lucide-react';
import { Order } from '@/types';
import { useMemo } from 'react';

interface CashierStatsProps {
  orders: Order[];
}

export function CashierStats({ orders }: CashierStatsProps) {
  const stats = useMemo(() => {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const todayOrders = orders.filter(order => new Date(order.created_at) >= todayStart);
    const weekOrders = orders.filter(order => new Date(order.created_at) >= weekStart);
    const pendingOrders = orders.filter(order => order.status === 'pending');

    return {
      todayRevenue: todayOrders.reduce((sum, order) => sum + order.total, 0),
      weeklyRevenue: weekOrders.reduce((sum, order) => sum + order.total, 0),
      todayOrders: todayOrders.length,
      weeklyOrders: weekOrders.length,
      pendingOrders: pendingOrders.length,
      averageOrderValue: todayOrders.length > 0 ? todayOrders.reduce((sum, order) => sum + order.total, 0) / todayOrders.length : 0,
    };
  }, [orders]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="Today's Revenue"
        value={stats.todayRevenue}
        change={12.5}
        icon={DollarSign}
        trend="up"
        formatValue="currency"
      />
      <StatCard
        title="Orders Today"
        value={stats.todayOrders}
        change={8.2}
        icon={ShoppingCart}
        trend="up"
      />
      <StatCard
        title="Pending Orders"
        value={stats.pendingOrders}
        change={-15.3}
        icon={Clock}
        trend="down"
      />
      <StatCard
        title="Avg Order Value"
        value={stats.averageOrderValue}
        change={5.7}
        icon={TrendingUp}
        trend="up"
        formatValue="currency"
      />
    </div>
  );
}