"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { 
  Trash2, 
  Eye, 
  EyeOff, 
  Download, 
  Upload, 
  MoreHorizontal,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface MenuBulkActionsProps {
  selectedCount: number;
  onBulkDelete: () => void;
  onBulkEnable: () => void;
  onBulkDisable: () => void;
  onExport: () => void;
  onImport: () => void;
  onClearSelection: () => void;
}

export function MenuBulkActions({
  selectedCount,
  onBulkDelete,
  onBulkEnable,
  onBulkDisable,
  onExport,
  onImport,
  onClearSelection
}: MenuBulkActionsProps) {
  if (selectedCount === 0) {
    return (
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={onExport}>
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
        <Button variant="outline" size="sm" onClick={onImport}>
          <Upload className="h-4 w-4 mr-2" />
          Import
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
        {selectedCount} selected
      </Badge>
      
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={onBulkEnable}>
          <Eye className="h-4 w-4 mr-2" />
          Enable
        </Button>
        
        <Button variant="outline" size="sm" onClick={onBulkDisable}>
          <EyeOff className="h-4 w-4 mr-2" />
          Disable
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-white border border-slate-200 shadow-lg">
            <DropdownMenuItem onClick={onExport} className="hover:bg-slate-50">
              <Download className="h-4 w-4 mr-2" />
              Export Selected
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={onBulkDelete} 
              className="hover:bg-red-50 text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Selected
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <Button variant="ghost" size="sm" onClick={onClearSelection}>
          <XCircle className="h-4 w-4 mr-2" />
          Clear
        </Button>
      </div>
    </div>
  );
}
