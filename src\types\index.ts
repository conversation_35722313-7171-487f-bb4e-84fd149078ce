// =============================================
// Application Types for Lupizza POS
// =============================================

import { Database } from './database';

// Database table types
export type Branch = Database['public']['Tables']['branches']['Row'];
export type User = Database['public']['Tables']['users']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type MenuItem = Database['public']['Tables']['menu_items']['Row'];
export type ItemVariant = Database['public']['Tables']['item_variants']['Row'];
export type Promotion = Database['public']['Tables']['promotions']['Row'];
export type Order = Database['public']['Tables']['orders']['Row'];
export type OrderItem = Database['public']['Tables']['order_items']['Row'];
export type Transaction = Database['public']['Tables']['transactions']['Row'];
export type ActivityLog = Database['public']['Tables']['activity_logs']['Row'];

// Insert types
export type BranchInsert = Database['public']['Tables']['branches']['Insert'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];
export type CategoryInsert = Database['public']['Tables']['categories']['Insert'];
export type MenuItemInsert = Database['public']['Tables']['menu_items']['Insert'];
export type ItemVariantInsert = Database['public']['Tables']['item_variants']['Insert'];
export type PromotionInsert = Database['public']['Tables']['promotions']['Insert'];
export type OrderInsert = Database['public']['Tables']['orders']['Insert'];
export type OrderItemInsert = Database['public']['Tables']['order_items']['Insert'];
export type TransactionInsert = Database['public']['Tables']['transactions']['Insert'];
export type ActivityLogInsert = Database['public']['Tables']['activity_logs']['Insert'];

// Update types
export type BranchUpdate = Database['public']['Tables']['branches']['Update'];
export type UserUpdate = Database['public']['Tables']['users']['Update'];
export type CategoryUpdate = Database['public']['Tables']['categories']['Update'];
export type MenuItemUpdate = Database['public']['Tables']['menu_items']['Update'];
export type ItemVariantUpdate = Database['public']['Tables']['item_variants']['Update'];
export type PromotionUpdate = Database['public']['Tables']['promotions']['Update'];
export type OrderUpdate = Database['public']['Tables']['orders']['Update'];
export type OrderItemUpdate = Database['public']['Tables']['order_items']['Update'];
export type TransactionUpdate = Database['public']['Tables']['transactions']['Update'];
export type ActivityLogUpdate = Database['public']['Tables']['activity_logs']['Update'];

// Enums
export type UserRole = 'admin' | 'cashier' | 'kitchen';
export type OrderType = 'dine_in' | 'take_away' | 'delivery_grab' | 'delivery_gojek';
export type OrderStatus = 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
export type PaymentMethod = 'cash' | 'qris' | 'card' | 'split';
export type PromoType = 'category' | 'item' | 'total';
export type DiscountType = 'percentage' | 'fixed';
export type VariantType = 'size' | 'temperature' | 'sauce' | 'portion';

// Extended types with relations
export interface MenuItemWithCategory extends MenuItem {
  categories: Category;
}

export interface MenuItemWithVariants extends MenuItem {
  item_variants: ItemVariant[];
  categories: Category;
}

export interface OrderWithItems extends Order {
  order_items: (OrderItem & {
    menu_items: MenuItem;
  })[];
  users: User;
  branches: Branch;
}

export interface OrderItemWithMenu extends OrderItem {
  menu_items: MenuItem;
}

// Cart types for frontend
export interface CartItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  selectedVariants: Record<string, string>; // variant_type -> variant_name
  notes?: string;
  unitPrice: number;
  totalPrice: number;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
}

// Variant selection types
export interface VariantSelection {
  variant_type: string;
  variant_name: string;
  price_adjustment: number;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Filter types for reports
export interface DateFilter {
  startDate: string;
  endDate: string;
}

export interface OrderFilter extends DateFilter {
  status?: OrderStatus;
  orderType?: OrderType;
  branchId?: string;
  cashierId?: string;
}

// Dashboard stats types
export interface DashboardStats {
  totalOrders: number;
  totalRevenue: number;
  pendingOrders: number;
  completedOrders: number;
  topSellingItems: Array<{
    menuItem: MenuItem;
    totalQuantity: number;
    totalRevenue: number;
  }>;
}

// Kitchen dashboard types
export interface KitchenOrder extends Order {
  order_items: Array<OrderItem & {
    menu_items: MenuItem;
  }>;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface MenuItemForm {
  name: string;
  description?: string;
  category_id: string;
  base_price: number;
  price_grab?: number;
  price_gojek?: number;
  image_url?: string;
  is_available: boolean;
  is_beverage: boolean;
}

export interface OrderForm {
  order_type: OrderType;
  table_number?: string;
  customer_name?: string;
  customer_phone?: string;
  notes?: string;
  items: CartItem[];
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Utility types
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Report types
export interface SalesReport {
  period: string;
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  topCategories: Array<{
    category: Category;
    revenue: number;
    orderCount: number;
  }>;
  topItems: Array<{
    menuItem: MenuItem;
    quantity: number;
    revenue: number;
  }>;
}

// Real-time types for kitchen
export interface RealtimePayload<T> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  schema: string;
  table: string;
}
