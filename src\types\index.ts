import { Database } from './database';

export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Menu = Database['public']['Tables']['menus']['Row'];
export type Order = Database['public']['Tables']['orders']['Row'];
export type OrderItem = Database['public']['Tables']['order_items']['Row'];
export type Transaction = Database['public']['Tables']['transactions']['Row'];
export type ActivityLog = Database['public']['Tables']['activity_logs']['Row'];

// Order status types
export type OrderStatus = 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
export type OrderItemStatus = 'pending' | 'preparing' | 'ready' | 'served';

export interface OrderWithItems extends Order {
  order_items: (OrderItem & {
    menu: Menu;
  })[];
  transaction?: Transaction;
}

export interface CartItem {
  menu_id: string;
  menu: Menu;
  quantity: number;
  notes?: string;
}

export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  todayRevenue: number;
  todayOrders: number;
  weeklyRevenue: number;
  monthlyRevenue: number;
}
