'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { authService } from '@/services/authService';
import { useQueryClient } from '@tanstack/react-query';
import {
  LayoutDashboard,
  ShoppingCart,
  ChefHat,
  Users,
  Menu as MenuIcon,
  Settings,
  BarChart3,
  Package,
  Receipt,
  Clock,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  LogOut,
} from 'lucide-react';
import toast from 'react-hot-toast';

interface SidebarProps {
  role: 'admin' | 'cashier' | 'kitchen';
}

export function Sidebar({ role }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      // Clear all cached data
      queryClient.clear();
      
      // Sign out
      await authService.signOut();
      
      // The authService.signOut() already handles redirect
      toast.success('Logged out successfully');
    } catch (error) {
      toast.error('Failed to logout');
      setIsLoggingOut(false);
    }
  };

  const getMenuItems = () => {
    const baseItems = [
      {
        title: 'Dashboard',
        href: `/dashboard/${role}`,
        icon: LayoutDashboard,
        badge: null,
      },
    ];

    switch (role) {
      case 'admin':
        return [
          ...baseItems,
          {
            title: 'Menu Management',
            href: '/dashboard/admin/menu',
            icon: MenuIcon,
            badge: null,
          },
          {
            title: 'User Management',
            href: '/dashboard/admin/users',
            icon: Users,
            badge: null,
          },
          {
            title: 'Reports',
            href: '/dashboard/admin/reports',
            icon: TrendingUp,
            badge: null,
          },
          {
            title: 'Settings',
            href: '/dashboard/admin/settings',
            icon: Settings,
            badge: null,
          },
        ];
      case 'cashier':
        return [
          ...baseItems,
          {
            title: 'New Order',
            href: '/dashboard/cashier/order',
            icon: ShoppingCart,
            badge: { text: 'Hot', color: 'bg-lupizza-red-500' },
          },
          {
            title: 'Orders',
            href: '/dashboard/cashier/orders',
            icon: Receipt,
            badge: { text: '12', color: 'bg-lupizza-green-500' },
          },
          {
            title: 'Menu',
            href: '/dashboard/cashier/menu',
            icon: MenuIcon,
            badge: null,
          },
          {
            title: 'Reports',
            href: '/dashboard/cashier/reports',
            icon: BarChart3,
            badge: null,
          },
        ];
      case 'kitchen':
        return [
          ...baseItems,
          {
            title: 'Order Queue',
            href: '/dashboard/kitchen/queue',
            icon: Clock,
            badge: { text: '5', color: 'bg-lupizza-red-500' },
          },
          {
            title: 'Menu Items',
            href: '/dashboard/kitchen/menu',
            icon: Package,
            badge: null,
          },
          {
            title: 'Completed',
            href: '/dashboard/kitchen/completed',
            icon: ChefHat,
            badge: null,
          },
        ];
      default:
        return baseItems;
    }
  };

  const menuItems = getMenuItems();

  const getRoleColor = () => {
    switch (role) {
      case 'admin':
        return 'from-lupizza-red-500 to-lupizza-red-600';
      case 'cashier':
        return 'from-lupizza-green-500 to-lupizza-green-600';
      case 'kitchen':
        return 'from-lupizza-cream-500 to-lupizza-cream-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div
      className={cn(
        'bg-white/80 backdrop-blur-xl border-r border-white/20 shadow-sm transition-all duration-300 flex flex-col',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-white/20">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-2">
              <div
                className={cn(
                  'w-3 h-3 rounded-full bg-gradient-to-r',
                  getRoleColor()
                )}
              ></div>
              <span className="text-sm font-semibold text-slate-700 capitalize">
                {role} Panel
              </span>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1.5 hover:bg-slate-100/50"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <Link key={item.href} href={item.href}>
              <div
                className={cn(
                  'group flex items-center px-2 py-2.5 rounded-xl transition-all duration-200 relative overflow-hidden',
                  isActive
                    ? 'bg-lupizza-green-50 text-lupizza-green-700 shadow-sm'
                    : 'text-slate-600 hover:bg-slate-50/50 hover:text-slate-900'
                )}
              >
                {isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-lupizza-green-500/10 to-transparent"></div>
                )}

                <div className="relative flex items-center space-x-3 flex-1 min-w-0">
                  <div
                    className={cn(
                      'p-1.5 rounded-lg transition-colors flex-shrink-0',
                      isActive
                        ? 'bg-lupizza-green-100'
                        : 'group-hover:bg-slate-100'
                    )}
                  >
                    <Icon
                      className={cn(
                        'h-4 w-4 transition-colors',
                        isActive ? 'text-lupizza-green-600' : 'text-slate-500'
                      )}
                    />
                  </div>

                  {!isCollapsed && (
                    <div className="flex items-center justify-between flex-1 min-w-0">
                      <span className="font-medium text-sm truncate">
                        {item.title}
                      </span>
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        {item.badge && (
                          <Badge
                            className={cn(
                              'text-xs px-2 py-0.5 text-white border-0',
                              item.badge.color
                            )}
                          >
                            {item.badge.text}
                          </Badge>
                        )}
                        {isActive && (
                          <Sparkles className="h-3 w-3 text-lupizza-green-500" />
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Link>
          );
        })}
      </nav>

      {/* Logout Button */}
      <div className="p-4 border-t border-white/20">
        <Button
          onClick={handleLogout}
          disabled={isLoggingOut}
          variant="ghost"
          className={cn(
            'w-full justify-start text-lupizza-red-600 hover:text-lupizza-red-700 hover:bg-lupizza-red-50 transition-all duration-200',
            isCollapsed ? 'px-2' : 'px-3'
          )}
        >
          <LogOut className={cn('h-4 w-4', !isCollapsed && 'mr-3')} />
          {!isCollapsed && (
            <span className="font-medium text-sm">
              {isLoggingOut ? 'Logging out...' : 'Logout'}
            </span>
          )}
        </Button>
      </div>

      {/* Footer */}
      {!isCollapsed && (
        <div className="p-4 border-t border-white/20">
          <div className="bg-gradient-to-r from-lupizza-green-50 to-lupizza-cream-50 rounded-xl p-3">
            <div className="flex items-center space-x-2 mb-2">
              <Sparkles className="h-4 w-4 text-lupizza-green-600" />
              <span className="text-sm font-semibold text-lupizza-green-700">
                Pro Tip
              </span>
            </div>
            <p className="text-xs text-lupizza-green-600">
              Use keyboard shortcuts to navigate faster!
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
