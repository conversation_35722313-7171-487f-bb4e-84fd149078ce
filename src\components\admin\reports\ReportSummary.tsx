"use client";

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { StatCard } from '@/components/ui/StatCard';
import { TrendingUp, DollarSign, ShoppingCart, Users, Target, Clock } from 'lucide-react';
import { format } from 'date-fns';

interface ReportSummaryProps {
  data: any[];
  reportType: 'sales' | 'menu' | 'staff';
  dateRange: { from: Date; to: Date };
}

export function ReportSummary({ data, reportType, dateRange }: ReportSummaryProps) {
  const getSalesStats = () => {
    const totalRevenue = data.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = data.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const completedOrders = data.filter(order => order.status === 'completed').length;
    const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;

    return [
      {
        title: 'Total Revenue',
        value: totalRevenue,
        format: 'currency' as const,
        icon: DollarSign,
        trend: 'up' as const,
        change: 12.5
      },
      {
        title: 'Total Orders',
        value: totalOrders,
        format: 'number' as const,
        icon: ShoppingCart,
        trend: 'up' as const,
        change: 8.2
      },
      {
        title: 'Avg Order Value',
        value: avgOrderValue,
        format: 'currency' as const,
        icon: Target,
        trend: 'up' as const,
        change: 5.1
      },
      {
        title: 'Completion Rate',
        value: completionRate,
        format: 'percentage' as const,
        icon: TrendingUp,
        trend: 'up' as const,
        change: 2.3
      }
    ];
  };

  const getMenuStats = () => {
    const totalItems = data.length;
    const totalQuantity = data.reduce((sum, item) => sum + item.quantity, 0);
    const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
    const avgPrice = totalQuantity > 0 ? totalRevenue / totalQuantity : 0;

    return [
      {
        title: 'Menu Items Sold',
        value: totalItems,
        format: 'number' as const,
        icon: ShoppingCart,
        trend: 'up' as const,
        change: 15.2
      },
      {
        title: 'Total Quantity',
        value: totalQuantity,
        format: 'number' as const,
        icon: Target,
        trend: 'up' as const,
        change: 18.7
      },
      {
        title: 'Total Revenue',
        value: totalRevenue,
        format: 'currency' as const,
        icon: DollarSign,
        trend: 'up' as const,
        change: 12.5
      },
      {
        title: 'Avg Item Price',
        value: avgPrice,
        format: 'currency' as const,
        icon: TrendingUp,
        trend: 'up' as const,
        change: 3.2
      }
    ];
  };

  const getStaffStats = () => {
    const totalStaff = data.length;
    const totalOrders = data.reduce((sum, staff) => sum + staff.ordersHandled, 0);
    const totalSales = data.reduce((sum, staff) => sum + staff.totalSales, 0);
    const avgOrdersPerStaff = totalStaff > 0 ? totalOrders / totalStaff : 0;

    return [
      {
        title: 'Active Staff',
        value: totalStaff,
        format: 'number' as const,
        icon: Users,
        trend: 'up' as const,
        change: 5.0
      },
      {
        title: 'Orders Handled',
        value: totalOrders,
        format: 'number' as const,
        icon: ShoppingCart,
        trend: 'up' as const,
        change: 12.8
      },
      {
        title: 'Total Sales',
        value: totalSales,
        format: 'currency' as const,
        icon: DollarSign,
        trend: 'up' as const,
        change: 15.3
      },
      {
        title: 'Avg Orders/Staff',
        value: avgOrdersPerStaff,
        format: 'number' as const,
        icon: Target,
        trend: 'up' as const,
        change: 8.1
      }
    ];
  };

  const getStats = () => {
    switch (reportType) {
      case 'sales':
        return getSalesStats();
      case 'menu':
        return getMenuStats();
      case 'staff':
        return getStaffStats();
      default:
        return [];
    }
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      {/* Period Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center space-x-2">
            <Clock className="h-5 w-5 text-lupizza-green-600" />
            <span>Report Period</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-600">From</p>
              <p className="font-medium">{format(dateRange.from, 'MMMM dd, yyyy')}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-slate-600">To</p>
              <p className="font-medium">{format(dateRange.to, 'MMMM dd, yyyy')}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-slate-600">Report Type</p>
              <p className="font-medium capitalize">{reportType} Report</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <StatCard
            key={stat.title}
            title={stat.title}
            value={stat.value}
            format={stat.format}
            icon={stat.icon}
            trend={stat.trend}
            change={stat.change}
          />
        ))}
      </div>
    </div>
  );
}