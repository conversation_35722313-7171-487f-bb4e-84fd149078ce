"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, TrendingUp } from 'lucide-react';

interface TopMenuItemsProps {
  orders: any[];
}

export function TopMenuItems({ orders }: TopMenuItemsProps) {
  // Calculate top menu items
  const menuStats = orders.reduce((acc, order) => {
    order.order_items?.forEach((item: any) => {
      const menuId = item.menu_id;
      const menuName = item.menu?.name || 'Unknown';
      
      if (!acc[menuId]) {
        acc[menuId] = {
          name: menuName,
          quantity: 0,
          revenue: 0,
          orders: new Set()
        };
      }
      
      acc[menuId].quantity += item.quantity;
      acc[menuId].revenue += item.price * item.quantity;
      acc[menuId].orders.add(order.id);
    });
    return acc;
  }, {} as Record<string, any>);

  const topItems = Object.values(menuStats)
    .map((item: any) => ({
      ...item,
      orderCount: item.orders.size
    }))
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, 5);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-base font-medium">Top Menu Items</CardTitle>
        <Crown className="h-4 w-4 text-lupizza-cream-600" />
      </CardHeader>
      <CardContent className="space-y-4">
        {topItems.length === 0 ? (
          <div className="text-center py-8 text-slate-500">
            <p>No menu data available</p>
          </div>
        ) : (
          topItems.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-lupizza-green-100 rounded-full">
                  <span className="text-sm font-bold text-lupizza-green-700">
                    {index + 1}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-slate-900">{item.name}</p>
                  <p className="text-sm text-slate-600">
                    {item.orderCount} orders
                  </p>
                </div>
              </div>
              <div className="text-right">
                <Badge variant="secondary" className="mb-1">
                  {item.quantity} sold
                </Badge>
                <p className="text-sm font-medium text-lupizza-green-600">
                  {new Intl.NumberFormat('id-ID', { 
                    style: 'currency', 
                    currency: 'IDR' 
                  }).format(item.revenue)}
                </p>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}