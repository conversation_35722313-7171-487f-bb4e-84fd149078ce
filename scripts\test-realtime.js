// Test Realtime Connection Script
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRealtime() {
  console.log('🔄 Testing Supabase Realtime...\n');

  try {
    // Test 1: Check if realtime is enabled for orders table
    console.log('1. Testing realtime subscription for orders...');
    
    const channel = supabase
      .channel('test-orders')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
        },
        (payload) => {
          console.log('✅ Realtime event received:', payload.eventType);
        }
      )
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
      });

    // Wait a bit for subscription to establish
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 2: Insert a test order to trigger realtime
    console.log('\n2. Creating test order to trigger realtime...');
    
    const testOrder = {
      order_number: `TEST-${Date.now()}`,
      order_type: 'dine_in',
      status: 'pending',
      subtotal: 50000,
      total_amount: 50000,
      cashier_id: '00000000-0000-0000-0000-000000000000', // placeholder
      branch_id: '550e8400-e29b-41d4-a716-446655440000', // from sample data
    };

    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .insert(testOrder)
      .select()
      .single();

    if (orderError) {
      console.log('⚠️  Could not create test order (this is expected if no authenticated user)');
      console.log('   Error:', orderError.message);
    } else {
      console.log('✅ Test order created:', orderData.order_number);
      
      // Clean up test order
      await supabase
        .from('orders')
        .delete()
        .eq('id', orderData.id);
      
      console.log('🧹 Test order cleaned up');
    }

    // Test 3: Check realtime for order_items
    console.log('\n3. Testing realtime subscription for order_items...');
    
    const itemsChannel = supabase
      .channel('test-order-items')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_items',
        },
        (payload) => {
          console.log('✅ Order items realtime event received:', payload.eventType);
        }
      )
      .subscribe((status) => {
        console.log('📡 Order items subscription status:', status);
      });

    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 4: Check channel status
    console.log('\n4. Checking channel status...');
    console.log('Orders channel state:', channel.state);
    console.log('Order items channel state:', itemsChannel.state);

    // Cleanup
    console.log('\n5. Cleaning up channels...');
    supabase.removeChannel(channel);
    supabase.removeChannel(itemsChannel);
    
    console.log('✅ Realtime test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Make sure RLS policies allow your user to access data');
    console.log('2. Create authenticated users for testing');
    console.log('3. Test realtime in the actual application');

  } catch (error) {
    console.error('❌ Realtime test failed:', error.message);
    console.log('\nTroubleshooting:');
    console.log('1. Make sure Realtime is enabled in Supabase dashboard');
    console.log('2. Check if tables are added to realtime publication');
    console.log('3. Verify RLS policies allow access to data');
  }
}

// Test RLS policies
async function testRLSPolicies() {
  console.log('\n🔒 Testing RLS Policies...\n');

  try {
    // Test reading from different tables without authentication
    const tables = ['branches', 'categories', 'menu_items', 'orders'];
    
    for (const table of tables) {
      console.log(`Testing ${table}...`);
      const { data, error } = await supabase
        .from(table)
        .select('count');
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        console.log(`✅ ${table}: Accessible (this might be expected for public data)`);
      }
    }

    console.log('\n📝 RLS Policy Test Notes:');
    console.log('- Errors are expected for tables with strict RLS policies');
    console.log('- Some tables (like categories, menu_items) might be readable by all users');
    console.log('- Orders and user-specific data should be restricted');

  } catch (error) {
    console.error('❌ RLS test failed:', error.message);
  }
}

async function runAllTests() {
  await testRealtime();
  await testRLSPolicies();
  process.exit(0);
}

runAllTests();
