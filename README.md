# LuPizza POS System

A complete Point of Sale (POS) system for LuPizza restaurant built with modern web technologies. This system supports three different user roles: Admin, Cashier, and Kitchen staff, each with their own dashboard and functionalities.

## 🚀 Tech Stack

- **Frontend**: Next.js 13 (App Router), React, TypeScript
- **Backend**: Supabase (Auth, Database, Real-time)
- **State Management**: React Query (TanStack Query)
- **Styling**: Tailwind CSS, ShadCN UI
- **Forms**: React Hook Form + Zod validation
- **Database**: PostgreSQL (via Supabase)
- **Real-time**: Supabase Real-time subscriptions
- **Authentication**: Supabase Auth

## 🎯 Features

### Admin Dashboard
- **Analytics**: Total revenue (daily, weekly, monthly)
- **Menu Management**: CRUD operations for food & beverage items
- **User Management**: Add/manage cashiers and kitchen staff
- **Reports**: Transaction reports with export capabilities
- **Settings**: System settings (tax rates, printer settings, promotions)

### Cashier Dashboard
- **Order Management**: Create orders for dine-in, takeaway, Gojek, Grab
- **Menu Selection**: Browse menu by categories
- **Payment Processing**: Support for cash, QRIS, card, and split payments
- **Receipt Printing**: Generate and print receipts
- **Real-time**: Auto-send orders to kitchen via Supabase channels

### Kitchen Dashboard
- **Order Queue**: Real-time order notifications
- **Status Tracking**: Mark items as preparing/ready/served
- **Order Details**: View order items, table numbers, special instructions
- **Fullscreen Mode**: Optimize for kitchen display
- **Sound Alerts**: Audio notifications for new orders

## 🏗️ Project Structure

```
/app/                    # Next.js App Router
├── login/              # Authentication page
├── dashboard/          # Role-based dashboards
│   ├── admin/         # Admin dashboard
│   ├── cashier/       # Cashier dashboard
│   └── kitchen/       # Kitchen dashboard
├── layout.tsx         # Root layout
├── page.tsx          # Root page (redirects based on role)
├── providers.tsx     # Query Client & Toast provider
└── globals.css       # Global styles

/components/            # Reusable components
├── shared/            # Shared components
│   ├── DashboardLayout.tsx
│   ├── Navbar.tsx
│   └── Sidebar.tsx
├── ui/               # ShadCN UI components
└── [role]/           # Role-specific components

/hooks/                # Custom React hooks
├── useUser.ts        # User authentication & profile
├── useOrders.ts      # Order management
└── useMenu.ts        # Menu management

/services/             # API service layers
├── authService.ts    # Authentication services
├── orderService.ts   # Order management services
└── menuService.ts    # Menu management services

/lib/                  # Utility libraries
├── supabase.ts       # Supabase client configuration
├── queryClient.ts    # React Query configuration
└── utils.ts          # Utility functions

/types/               # TypeScript type definitions
├── database.ts       # Database schema types
└── index.ts          # Common types

/supabase/            # Supabase configuration
└── migrations/       # Database migrations
    ├── create_database_schema.sql
    └── seed_sample_data.sql
```

## 📦 Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd lupizza-pos
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **Set up Supabase database**
   - Create a new Supabase project
   - Run the migration files in your Supabase SQL editor:
     - `supabase/migrations/create_database_schema.sql`
     - `supabase/migrations/seed_sample_data.sql`

5. **Create user accounts**
   - Use Supabase Auth to create user accounts
   - Insert user profiles into the `profiles` table with appropriate roles

6. **Start the development server**
```bash
npm run dev
```

## 🔐 User Roles & Access

### Admin Users
- **Access**: `/dashboard/admin`
- **Permissions**: Full system access, analytics, user management, system settings
- **Features**: Revenue analytics, menu CRUD, user management, reports

### Cashier Users
- **Access**: `/dashboard/cashier`
- **Permissions**: Create orders, view sales data, process payments
- **Features**: POS interface, order creation, payment processing, receipt printing

### Kitchen Users
- **Access**: `/dashboard/kitchen`
- **Permissions**: View orders, update order status, manage kitchen queue
- **Features**: Order queue, status updates, real-time notifications

## 🗄️ Database Schema

### Core Tables
- `profiles` - User profiles with role-based access
- `menus` - Food and beverage items
- `orders` - Customer orders
- `order_items` - Individual items within orders
- `transactions` - Payment records
- `activity_logs` - System activity logging

### Key Relationships
- Orders belong to cashiers (profiles)
- Order items reference menus and orders
- Transactions are linked to orders
- Activity logs track user actions

## 🔄 Real-time Features

### Kitchen Notifications
- Real-time order notifications via Supabase channels
- Automatic status updates across all connected clients
- Sound alerts for new orders

### Order Status Updates
- Live status tracking from kitchen to cashier
- Real-time inventory updates
- Live dashboard statistics

## 📱 Responsive Design

- **Mobile-first approach** with responsive breakpoints
- **Touch-friendly interface** for tablet POS systems
- **Fullscreen mode** for kitchen displays
- **Print-optimized** receipt layouts

## 🚀 Deployment

### Option 1: Vercel (Recommended)
```bash
npm run build
```
Deploy to Vercel with automatic Next.js configuration

### Option 2: Netlify
```bash
npm run build
npm run export
```
Deploy the `out` directory to Netlify

### Option 3: Docker
```bash
docker build -t lupizza-pos .
docker run -p 3000:3000 lupizza-pos
```

## 🧪 Testing

```bash
# Run tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 📊 Performance Optimizations

- **React Query** for efficient data fetching and caching
- **Next.js Image optimization** for menu item images
- **Supabase connection pooling** for database performance
- **Lazy loading** for dashboard components
- **Memoization** for expensive calculations

## 🛡️ Security Features

- **Row Level Security (RLS)** on all database tables
- **Role-based access control** with strict permissions
- **JWT authentication** via Supabase Auth
- **Input validation** with Zod schemas
- **SQL injection protection** via Supabase client

## 🔧 Configuration

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Supabase Configuration
- Enable Row Level Security
- Set up Real-time subscriptions
- Configure Auth settings
- Set up database functions and triggers

## 📈 Future Enhancements

- **Inventory Management**: Track stock levels and low-stock alerts
- **Customer Management**: Customer profiles and loyalty programs
- **Advanced Analytics**: Sales forecasting and trend analysis
- **Multi-location Support**: Support for multiple restaurant locations
- **Mobile App**: React Native app for staff
- **Integration APIs**: Connect with third-party services (delivery platforms, payment gateways)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, please contact:
- Email: <EMAIL>
- GitHub Issues: Create an issue in this repository
- Documentation: Check the `/docs` folder for detailed guides

## 🙏 Acknowledgments

- **Supabase** for the excellent backend-as-a-service platform
- **Vercel** for seamless Next.js deployment
- **ShadCN UI** for beautiful, accessible UI components
- **Tailwind CSS** for utility-first styling
- **React Query** for powerful data synchronization

---

**Built with ❤️ for LuPizza Restaurant**