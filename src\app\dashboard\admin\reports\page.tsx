"use client";

import { useState } from 'react';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { ReportFilters } from '@/components/admin/reports/ReportFilters';
import { ReportTable } from '@/components/admin/reports/ReportTable';
import { ReportSummary } from '@/components/admin/reports/ReportSummary';
import { ExportButtons } from '@/components/admin/reports/ExportButtons';
import { DateRangePicker } from '@/components/ui/DateRangePicker';
import { useReports } from '@/hooks/useReports';
import { useExport } from '@/hooks/useExport';
import { subDays } from 'date-fns';
import { FileText, Download } from 'lucide-react';

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 7),
    to: new Date()
  });
  const [reportType, setReportType] = useState<'sales' | 'menu' | 'staff'>('sales');
  const [exportFormat, setExportFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf');

  const { data: reportData, isLoading } = useReports(reportType, dateRange);
  const { exportReport, isExporting } = useExport();

  const handleExport = async () => {
    try {
      await exportReport({
        type: reportType,
        format: exportFormat,
        dateRange,
        data: reportData
      });
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Reports & Analytics</h1>
            <p className="text-slate-600 mt-1">Generate and export detailed business reports</p>
          </div>
          
          <ExportButtons
            onExport={handleExport}
            isExporting={isExporting}
            format={exportFormat}
            onFormatChange={setExportFormat}
          />
        </div>

        {/* Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <ReportFilters
            reportType={reportType}
            onReportTypeChange={setReportType}
          />
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />
        </div>

        {/* Report Summary */}
        <ReportSummary
          data={reportData}
          reportType={reportType}
          dateRange={dateRange}
        />

        {/* Report Table */}
        <ReportTable
          data={reportData}
          reportType={reportType}
          isLoading={isLoading}
        />
      </div>
    </DashboardLayout>
  );
}