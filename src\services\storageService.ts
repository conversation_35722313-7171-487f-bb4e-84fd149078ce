import { supabase } from '@/lib/supabase';

export class StorageService {
  private static readonly BUCKET_NAME = 'menu-images';

  /**
   * Initialize storage bucket if it doesn't exist
   */
  static async initializeBucket() {
    try {
      // Check if bucket exists
      const { data: buckets } = await supabase.storage.listBuckets();
      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME);

      if (!bucketExists) {
        // Create bucket if it doesn't exist
        const { error } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
          fileSizeLimit: 5242880, // 5MB
        });

        if (error) {
          console.error('Error creating storage bucket:', error);
          throw error;
        }
      }
    } catch (error) {
      console.error('Error initializing storage bucket:', error);
      throw error;
    }
  }

  /**
   * Upload menu image file
   */
  static async uploadMenuImage(file: File, menuId?: string): Promise<string> {
    try {
      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = menuId 
        ? `menu-${menuId}-${Date.now()}.${fileExt}`
        : `menu-${Date.now()}.${fileExt}`;
      
      const filePath = `menus/${fileName}`;

      // Upload file
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Error uploading file:', error);
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading menu image:', error);
      throw error;
    }
  }

  /**
   * Update menu image (delete old and upload new)
   */
  static async updateMenuImage(file: File, menuId: string, oldImageUrl?: string): Promise<string> {
    try {
      // Delete old image if exists
      if (oldImageUrl) {
        await this.deleteMenuImage(oldImageUrl);
      }

      // Upload new image
      return await this.uploadMenuImage(file, menuId);
    } catch (error) {
      console.error('Error updating menu image:', error);
      throw error;
    }
  }

  /**
   * Delete menu image
   */
  static async deleteMenuImage(imageUrl: string): Promise<void> {
    try {
      // Extract file path from URL
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');
      const bucketIndex = pathParts.findIndex(part => part === this.BUCKET_NAME);
      
      if (bucketIndex === -1) {
        console.warn('Invalid image URL format:', imageUrl);
        return;
      }

      const filePath = pathParts.slice(bucketIndex + 1).join('/');

      // Delete file
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.error('Error deleting file:', error);
        // Don't throw error for delete operations to avoid blocking other operations
      }
    } catch (error) {
      console.error('Error deleting menu image:', error);
      // Don't throw error for delete operations
    }
  }

  /**
   * Get optimized image URL with transformations
   */
  static getOptimizedImageUrl(imageUrl: string, options?: {
    width?: number;
    height?: number;
    quality?: number;
  }): string {
    try {
      const url = new URL(imageUrl);
      const params = new URLSearchParams();

      if (options?.width) params.set('width', options.width.toString());
      if (options?.height) params.set('height', options.height.toString());
      if (options?.quality) params.set('quality', options.quality.toString());

      if (params.toString()) {
        url.search = params.toString();
      }

      return url.toString();
    } catch (error) {
      console.error('Error generating optimized image URL:', error);
      return imageUrl;
    }
  }

  /**
   * Validate image file
   */
  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Maximum size is 5MB.'
      };
    }

    return { isValid: true };
  }
}

export const storageService = StorageService;
