"use client";

import { useMemo } from 'react';
import Link from 'next/link';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { CashierStats } from '@/components/cashier/CashierStats';
import { ChartCard } from '@/components/ui/ChartCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useOrders } from '@/hooks/useOrders';
import { 
  Plus, 
  Calendar,
  Filter,
  Receipt,
  ShoppingCart,
  TrendingUp,
  CreditCard,
  Smartphone,
  Banknote
} from 'lucide-react';

export default function CashierDashboard() {
  const { data: orders = [] } = useOrders();

  const recentOrders = useMemo(() => {
    return orders
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5);
  }, [orders]);

  const paymentMethodsData = useMemo(() => {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayOrders = orders.filter(order => new Date(order.created_at) >= todayStart);

    // Mock payment method data - replace with actual transaction data
    return [
      { method: 'Cash', count: 12, amount: 850000, icon: Banknote, color: 'bg-green-100 text-green-600' },
      { method: 'QRIS', count: 8, amount: 420000, icon: Smartphone, color: 'bg-blue-100 text-blue-600' },
      { method: 'Card', count: 3, amount: 140000, icon: CreditCard, color: 'bg-purple-100 text-purple-600' }
    ];
  }, [orders]);

  return (
    <DashboardLayout requiredRole="cashier">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Cashier Dashboard
            </h1>
            <p className="text-slate-600 mt-1">Manage orders and track daily sales</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
              <Calendar className="h-4 w-4 mr-2" />
              Today
            </Button>
            <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Link href="/dashboard/cashier/order">
              <Button className="bg-lupizza-gradient hover:shadow-lupizza text-white">
                <Plus className="h-4 w-4 mr-2" />
                New Order
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { href: '/dashboard/cashier/order', title: 'New Order', desc: 'Create new order', icon: Plus, color: 'lupizza-green' },
            { href: '/dashboard/cashier/orders', title: 'View Orders', desc: 'Manage all orders', icon: Receipt, color: 'blue' },
            { href: '/dashboard/cashier/menu', title: 'Menu', desc: 'Browse menu items', icon: ShoppingCart, color: 'purple' },
            { href: '/dashboard/cashier/reports', title: 'Reports', desc: 'View sales reports', icon: TrendingUp, color: 'orange' }
          ].map((action) => {
            const Icon = action.icon;
            return (
              <Link key={action.href} href={action.href}>
                <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer bg-gradient-to-br from-white to-slate-50 border-slate-200">
                  <CardContent className="p-6 text-center">
                    <div className={`w-12 h-12 bg-${action.color}-500 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className={`font-semibold text-${action.color}-700`}>{action.title}</h3>
                    <p className={`text-sm text-${action.color}-600 mt-1`}>{action.desc}</p>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* Stats Cards */}
        <CashierStats orders={orders} />

        {/* Recent Orders & Payment Methods */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <ChartCard title="Recent Orders">
            <div className="space-y-3">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-lupizza-green-100 rounded-xl flex items-center justify-center">
                      <span className="text-sm font-semibold text-lupizza-green-600">
                        #{order.id.slice(-3)}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-slate-900">
                        Order #{order.id.slice(-6)}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {order.order_type}
                        </Badge>
                        {order.table_number && (
                          <span className="text-xs text-slate-500">
                            Table {order.table_number}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-slate-900">
                      Rp {order.total.toLocaleString('id-ID')}
                    </p>
                    <Badge 
                      variant={order.status === 'completed' ? 'default' : 'secondary'}
                      className={`text-xs mt-1 ${
                        order.status === 'completed' 
                          ? 'bg-lupizza-green-500' 
                          : order.status === 'processing'
                          ? 'bg-blue-500'
                          : 'bg-yellow-500'
                      }`}
                    >
                      {order.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </ChartCard>

          {/* Payment Methods */}
          <ChartCard title="Payment Methods Today">
            <div className="space-y-4">
              {paymentMethodsData.map((payment) => {
                const Icon = payment.icon;
                const percentage = Math.round((payment.amount / paymentMethodsData.reduce((sum, p) => sum + p.amount, 0)) * 100);
                
                return (
                  <div key={payment.method} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${payment.color}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900">{payment.method}</p>
                        <p className="text-sm text-slate-500">{payment.count} transactions</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">Rp {payment.amount.toLocaleString('id-ID')}</p>
                      <p className="text-xs text-slate-500">{percentage}%</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </ChartCard>
        </div>
      </div>
    </DashboardLayout>
  );
}
