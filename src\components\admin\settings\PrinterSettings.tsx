"use client";

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Printer, TestTube, Save, Wifi, Usb } from 'lucide-react';
import toast from 'react-hot-toast';

export function PrinterSettings() {
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [printerType, setPrinterType] = useState('thermal');
  const [connectionType, setConnectionType] = useState('usb');
  const [autoprint, setAutoprint] = useState(true);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Printer settings saved successfully');
    } catch (error) {
      toast.error('Failed to save printer settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestPrint = async () => {
    setIsTesting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Test print sent successfully');
    } catch (error) {
      toast.error('Failed to send test print');
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Printer className="h-5 w-5 text-lupizza-green-600" />
            <span>Receipt Printer Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Printer Type */}
          <div className="space-y-2">
            <Label>Printer Type</Label>
            <Select value={printerType} onValueChange={setPrinterType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="thermal">Thermal Printer (58mm/80mm)</SelectItem>
                <SelectItem value="inkjet">Inkjet Printer</SelectItem>
                <SelectItem value="laser">Laser Printer</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Connection Type */}
          <div className="space-y-2">
            <Label>Connection Type</Label>
            <Select value={connectionType} onValueChange={setConnectionType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="usb">
                  <div className="flex items-center space-x-2">
                    <Usb className="h-4 w-4" />
                    <span>USB Connection</span>
                  </div>
                </SelectItem>
                <SelectItem value="network">
                  <div className="flex items-center space-x-2">
                    <Wifi className="h-4 w-4" />
                    <span>Network (IP Address)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Connection Details */}
          {connectionType === 'network' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="printer_ip">Printer IP Address</Label>
                <Input
                  id="printer_ip"
                  placeholder="*************"
                  defaultValue="*************"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="printer_port">Port</Label>
                <Input
                  id="printer_port"
                  placeholder="9100"
                  defaultValue="9100"
                />
              </div>
            </div>
          )}

          {connectionType === 'usb' && (
            <div className="space-y-2">
              <Label htmlFor="printer_name">Printer Name</Label>
              <Input
                id="printer_name"
                placeholder="POS-80"
                defaultValue="POS-80"
              />
            </div>
          )}

          <Separator />

          {/* Print Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Print Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="paper_width">Paper Width (mm)</Label>
                <Select defaultValue="80">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="58">58mm</SelectItem>
                    <SelectItem value="80">80mm</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="font_size">Font Size</Label>
                <Select defaultValue="medium">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">Auto Print Orders</Label>
                <p className="text-sm text-slate-600">Automatically print receipts when orders are created</p>
              </div>
              <Switch checked={autoprint} onCheckedChange={setAutoprint} />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">Print Kitchen Copy</Label>
                <p className="text-sm text-slate-600">Print a copy for kitchen staff</p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>

          <Separator />

          {/* Receipt Header/Footer */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Receipt Customization</h3>
            
            <div className="space-y-2">
              <Label htmlFor="receipt_header">Receipt Header</Label>
              <Textarea
                id="receipt_header"
                placeholder="Enter header text..."
                defaultValue="LuPizza Restaurant&#10;Jl. Raya No. 123&#10;Telp: (*************"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="receipt_footer">Receipt Footer</Label>
              <Textarea
                id="receipt_footer"
                placeholder="Enter footer text..."
                defaultValue="Terima kasih atas kunjungan Anda!&#10;Selamat menikmati!"
                rows={2}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={handleTestPrint}
              disabled={isTesting}
            >
              {isTesting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-600 mr-2"></div>
                  Testing...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4 mr-2" />
                  Test Print
                </>
              )}
            </Button>

            <Button
              onClick={handleSave}
              disabled={isLoading}
              className="bg-lupizza-green-600 hover:bg-lupizza-green-700"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Receipt Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-white border-2 border-dashed border-slate-300 p-4 font-mono text-sm max-w-sm mx-auto">
            <div className="text-center mb-4">
              <div className="font-bold">LuPizza Restaurant</div>
              <div>Jl. Raya No. 123</div>
              <div>Telp: (*************</div>
            </div>
            <div className="border-t border-dashed border-slate-400 my-2"></div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Order #12345</span>
                <span>Dine In</span>
              </div>
              <div className="flex justify-between">
                <span>Table 5</span>
                <span>12/01/24 14:30</span>
              </div>
            </div>
            <div className="border-t border-dashed border-slate-400 my-2"></div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>2x Margherita Pizza</span>
                <span>120,000</span>
              </div>
              <div className="flex justify-between">
                <span>1x Coca Cola</span>
                <span>15,000</span>
              </div>
            </div>
            <div className="border-t border-dashed border-slate-400 my-2"></div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>135,000</span>
              </div>
              <div className="flex justify-between">
                <span>Tax (10%)</span>
                <span>13,500</span>
              </div>
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>148,500</span>
              </div>
            </div>
            <div className="border-t border-dashed border-slate-400 my-2"></div>
            <div className="text-center text-xs">
              <div>Terima kasih atas kunjungan Anda!</div>
              <div>Selamat menikmati!</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}