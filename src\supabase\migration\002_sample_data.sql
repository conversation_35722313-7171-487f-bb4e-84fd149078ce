-- =============================================
-- Sample Data for Lupizza POS
-- =============================================

-- Insert sample branch
INSERT INTO
    branches (id, name, address, phone)
VALUES (
        '550e8400-e29b-41d4-a716-************',
        'Lupizza Main Branch',
        'Jl. Malioboro No. 123, Yogyakarta',
        '0274-123456'
    );

-- Insert categories sesuai requirement
INSERT INTO
    categories (
        id,
        name,
        description,
        sort_order
    )
VALUES (
        '550e8400-e29b-41d4-a716-************',
        'Minuman',
        'Berbagai macam minuman segar',
        1
    ),
    (
        '550e8400-e29b-41d4-a716-446655440002',
        'Pizza',
        'Pizza dengan berbagai topping',
        2
    ),
    (
        '550e8400-e29b-41d4-a716-446655440003',
        'Steak',
        'Steak dengan berbagai saus',
        3
    ),
    (
        '550e8400-e29b-41d4-a716-446655440004',
        'Pasta',
        'Pasta dengan saus Bolognese',
        4
    ),
    (
        '550e8400-e29b-41d4-a716-446655440005',
        'Snack',
        'Berbagai macam snack',
        5
    );

-- Insert menu items sesuai requirement
-- MINUMAN (dengan pilihan Es/Hot)
INSERT INTO
    menu_items (
        id,
        name,
        description,
        category_id,
        base_price,
        is_beverage
    )
VALUES (
        '550e8400-e29b-41d4-a716-446655440010',
        'Caffe Latte',
        'Kopi dengan susu steamed',
        '550e8400-e29b-41d4-a716-************',
        25000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440011',
        'Capuchino',
        'Espresso dengan foam susu',
        '550e8400-e29b-41d4-a716-************',
        23000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440012',
        'Mochacino',
        'Kopi dengan coklat dan susu',
        '550e8400-e29b-41d4-a716-************',
        27000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440013',
        'Kopi Susu Gula Aren',
        'Kopi dengan gula aren',
        '550e8400-e29b-41d4-a716-************',
        20000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440014',
        'Matcha Latte',
        'Teh hijau dengan susu',
        '550e8400-e29b-41d4-a716-************',
        28000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440015',
        'Orange',
        'Jus jeruk segar',
        '550e8400-e29b-41d4-a716-************',
        15000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440016',
        'Lemon Tea',
        'Teh dengan lemon',
        '550e8400-e29b-41d4-a716-************',
        12000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440017',
        'Lecy Tea',
        'Teh dengan leci',
        '550e8400-e29b-41d4-a716-************',
        15000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440018',
        'Coklat',
        'Minuman coklat hangat',
        '550e8400-e29b-41d4-a716-************',
        18000,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440019',
        'Thai Tea',
        'Teh Thailand',
        '550e8400-e29b-41d4-a716-************',
        16000,
        true
    );

-- PIZZA (dengan pilihan ukuran)
INSERT INTO
    menu_items (
        id,
        name,
        description,
        category_id,
        base_price
    )
VALUES (
        '550e8400-e29b-41d4-a716-446655440020',
        'Pizza Napoletana',
        'Pizza klasik dengan topping tradisional',
        '550e8400-e29b-41d4-a716-446655440002',
        45000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440021',
        'Chicken Breast',
        'Pizza dengan daging ayam',
        '550e8400-e29b-41d4-a716-446655440002',
        50000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440022',
        'Pizza Tuna',
        'Pizza dengan tuna',
        '550e8400-e29b-41d4-a716-446655440002',
        48000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440023',
        'Big Crush Meat Mania Mozzarella',
        'Pizza dengan berbagai daging',
        '550e8400-e29b-41d4-a716-446655440002',
        65000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440024',
        'Big Crush Beef Sausage Mozzarella',
        'Pizza dengan sosis sapi',
        '550e8400-e29b-41d4-a716-446655440002',
        60000
    );

-- STEAK (dengan pilihan saus dan porsi)
INSERT INTO
    menu_items (
        id,
        name,
        description,
        category_id,
        base_price
    )
VALUES (
        '550e8400-e29b-41d4-a716-446655440030',
        'Chicken Steak',
        'Steak ayam juicy',
        '550e8400-e29b-41d4-a716-446655440003',
        35000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440031',
        'Tenderloin Steak',
        'Steak tenderloin premium',
        '550e8400-e29b-41d4-a716-446655440003',
        75000
    ),
    (
        '550e8400-e29b-41d4-a716-************',
        'Beef Steak',
        'Steak daging sapi',
        '550e8400-e29b-41d4-a716-446655440003',
        55000
    );

-- PASTA (dengan saus Bolognese)
INSERT INTO
    menu_items (
        id,
        name,
        description,
        category_id,
        base_price
    )
VALUES (
        '550e8400-e29b-41d4-a716-446655440040',
        'Spaghetti',
        'Spaghetti dengan saus Bolognese',
        '550e8400-e29b-41d4-a716-446655440004',
        30000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440041',
        'Penne',
        'Penne dengan saus Bolognese',
        '550e8400-e29b-41d4-a716-446655440004',
        32000
    );

-- SNACK
INSERT INTO
    menu_items (
        id,
        name,
        description,
        category_id,
        base_price
    )
VALUES (
        '550e8400-e29b-41d4-a716-446655440050',
        'French Fries',
        'Kentang goreng crispy',
        '550e8400-e29b-41d4-a716-446655440005',
        15000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440051',
        'Chicken Wings',
        'Sayap ayam pedas',
        '550e8400-e29b-41d4-a716-446655440005',
        25000
    ),
    (
        '550e8400-e29b-41d4-a716-446655440052',
        'Onion Rings',
        'Bawang bombay goreng',
        '550e8400-e29b-41d4-a716-446655440005',
        18000
    );

-- =============================================
-- ITEM VARIANTS sesuai requirement
-- =============================================

-- Varian untuk MINUMAN (Es/Hot)
INSERT INTO
    item_variants (
        menu_item_id,
        variant_type,
        variant_name,
        price_adjustment,
        is_default
    )
VALUES
    -- Caffe Latte
    (
        '550e8400-e29b-41d4-a716-446655440010',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440010',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Capuchino
    (
        '550e8400-e29b-41d4-a716-446655440011',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440011',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Mochacino
    (
        '550e8400-e29b-41d4-a716-446655440012',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440012',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Kopi Susu Gula Aren
    (
        '550e8400-e29b-41d4-a716-446655440013',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440013',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Matcha Latte
    (
        '550e8400-e29b-41d4-a716-446655440014',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440014',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Orange (hanya Ice)
    (
        '550e8400-e29b-41d4-a716-446655440015',
        'temperature',
        'Ice',
        0,
        true
    ),
    -- Lemon Tea
    (
        '550e8400-e29b-41d4-a716-446655440016',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440016',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Lecy Tea
    (
        '550e8400-e29b-41d4-a716-446655440017',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440017',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Coklat
    (
        '550e8400-e29b-41d4-a716-446655440018',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440018',
        'temperature',
        'Ice',
        0,
        false
    ),
    -- Thai Tea
    (
        '550e8400-e29b-41d4-a716-446655440019',
        'temperature',
        'Hot',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440019',
        'temperature',
        'Ice',
        0,
        false
    );

-- Varian untuk PIZZA (Ukuran)
-- Pizza Napoletana, Chicken Breast, Pizza Tuna (S, M, L)
INSERT INTO
    item_variants (
        menu_item_id,
        variant_type,
        variant_name,
        price_adjustment,
        is_default
    )
VALUES
    -- Pizza Napoletana
    (
        '550e8400-e29b-41d4-a716-446655440020',
        'size',
        'S',
        -10000,
        false
    ),
    (
        '550e8400-e29b-41d4-a716-446655440020',
        'size',
        'M',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440020',
        'size',
        'L',
        15000,
        false
    ),
    -- Chicken Breast
    (
        '550e8400-e29b-41d4-a716-446655440021',
        'size',
        'S',
        -10000,
        false
    ),
    (
        '550e8400-e29b-41d4-a716-446655440021',
        'size',
        'M',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440021',
        'size',
        'L',
        15000,
        false
    ),
    -- Pizza Tuna
    (
        '550e8400-e29b-41d4-a716-446655440022',
        'size',
        'S',
        -10000,
        false
    ),
    (
        '550e8400-e29b-41d4-a716-446655440022',
        'size',
        'M',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440022',
        'size',
        'L',
        15000,
        false
    ),
    -- Big Crush Meat Mania (M, L)
    (
        '550e8400-e29b-41d4-a716-446655440023',
        'size',
        'M',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440023',
        'size',
        'L',
        20000,
        false
    ),
    -- Big Crush Beef Sausage (M, L)
    (
        '550e8400-e29b-41d4-a716-446655440024',
        'size',
        'M',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440024',
        'size',
        'L',
        20000,
        false
    );

-- Varian untuk STEAK (Saus dan Porsi)
INSERT INTO
    item_variants (
        menu_item_id,
        variant_type,
        variant_name,
        price_adjustment,
        is_default
    )
VALUES
    -- Chicken Steak (Saus)
    (
        '550e8400-e29b-41d4-a716-446655440030',
        'sauce',
        'Mushroom',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440030',
        'sauce',
        'Black Pepper',
        0,
        false
    ),
    -- Chicken Steak (Porsi)
    (
        '550e8400-e29b-41d4-a716-446655440030',
        'portion',
        'Single',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440030',
        'portion',
        'Double',
        25000,
        false
    ),
    -- Tenderloin Steak (Saus)
    (
        '550e8400-e29b-41d4-a716-446655440031',
        'sauce',
        'Mushroom',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440031',
        'sauce',
        'Black Pepper',
        0,
        false
    ),
    -- Tenderloin Steak (Porsi)
    (
        '550e8400-e29b-41d4-a716-446655440031',
        'portion',
        'Single',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-446655440031',
        'portion',
        'Double',
        50000,
        false
    ),
    -- Beef Steak (Saus, hanya Single)
    (
        '550e8400-e29b-41d4-a716-************',
        'sauce',
        'Mushroom',
        0,
        true
    ),
    (
        '550e8400-e29b-41d4-a716-************',
        'sauce',
        'Black Pepper',
        0,
        false
    ),
    (
        '550e8400-e29b-41d4-a716-************',
        'portion',
        'Single',
        0,
        true
    );

-- Sample users (akan dibuat melalui Supabase Auth, ini hanya contoh struktur)
-- Admin user akan dibuat manual melalui Supabase dashboard
-- INSERT INTO users (id, email, full_name, role, branch_id) VALUES
-- ('admin-uuid', '<EMAIL>', 'Admin Lupizza', 'admin', '550e8400-e29b-41d4-a716-************'),
-- ('cashier-uuid', '<EMAIL>', 'Kasir 1', 'cashier', '550e8400-e29b-41d4-a716-************'),
-- ('kitchen-uuid', '<EMAIL>', 'Chef 1', 'kitchen', '550e8400-e29b-41d4-a716-************');

-- Sample promotion
INSERT INTO
    promotions (
        id,
        name,
        description,
        promo_type,
        discount_type,
        discount_value,
        target_category_id,
        start_date,
        end_date
    )
VALUES (
        '550e8400-e29b-41d4-a716-************',
        'Diskon Minuman 10%',
        'Diskon 10% untuk semua minuman',
        'category',
        'percentage',
        10,
        '550e8400-e29b-41d4-a716-************',
        '2024-01-01 00:00:00+07',
        '2024-12-31 23:59:59+07'
    );