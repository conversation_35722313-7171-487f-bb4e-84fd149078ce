"use client";

import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { GeneralSettings } from '@/components/admin/settings/GeneralSettings';
import { TaxSettings } from '@/components/admin/settings/TaxSettings';
import { PrinterSettings } from '@/components/admin/settings/PrinterSettings';
import { NotificationSettings } from '@/components/admin/settings/NotificationSettings';
import { BackupSettings } from '@/components/admin/settings/BackupSettings';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Settings, Receipt, Bell, Database, Percent } from 'lucide-react';

export default function SettingsPage() {
  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-slate-900">System Settings</h1>
          <p className="text-slate-600 mt-1">Configure system preferences and business settings</p>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="tax" className="flex items-center gap-2">
              <Percent className="h-4 w-4" />
              Tax & Pricing
            </TabsTrigger>
            <TabsTrigger value="printer" className="flex items-center gap-2">
              <Receipt className="h-4 w-4" />
              Printer
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="backup" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Backup
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <GeneralSettings />
          </TabsContent>

          <TabsContent value="tax">
            <TaxSettings />
          </TabsContent>

          <TabsContent value="printer">
            <PrinterSettings />
          </TabsContent>

          <TabsContent value="notifications">
            <NotificationSettings />
          </TabsContent>

          <TabsContent value="backup">
            <BackupSettings />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}