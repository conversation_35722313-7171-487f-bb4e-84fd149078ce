'use client';

import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: number;
  format: 'currency' | 'number' | 'percentage';
  icon: LucideIcon;
  trend?: 'up' | 'down';
  change?: number;
  className?: string;
}

export function StatCard({
  title,
  value,
  format,
  icon: Icon,
  trend,
  change,
  className = '',
}: StatCardProps) {
  const formatValue = (val: number, fmt: string) => {
    switch (fmt) {
      case 'currency':
        return new Intl.NumberFormat('id-ID', {
          style: 'currency',
          currency: 'IDR',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(val);
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'number':
        return new Intl.NumberFormat('id-ID').format(val);
      default:
        return val.toString();
    }
  };

  return (
    <Card
      className={`overflow-hidden border-0 shadow-sm bg-white/50 backdrop-blur-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02] ${className}`}
    >
      <CardContent className="p-6">
        <div className="space-y-3">
          {/* Header with Icon and Trend */}
          <div className="flex items-center justify-between">
            <div className="w-10 h-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100/50 border border-green-200/50">
              <Icon className="h-5 w-5 text-green-600" />
            </div>
            {trend && change !== undefined && (
              <div
                className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                  trend === 'up'
                    ? 'bg-green-50 text-green-700 border border-green-200/50'
                    : 'bg-red-50 text-red-700 border border-red-200/50'
                }`}
              >
                {trend === 'up' ? (
                  <TrendingUp className="w-3 h-3" />
                ) : (
                  <TrendingDown className="w-3 h-3 rotate-180" />
                )}
                <span>{Math.abs(change)}%</span>
              </div>
            )}
          </div>

          {/* Main Value */}
          <div className="space-y-1">
            <div className="text-2xl font-bold text-slate-900 tracking-tight">
              {formatValue(value, format)}
            </div>
            <div className="text-sm font-medium text-slate-600">{title}</div>
          </div>

          {/* Subtitle or Trend Label */}
          {trend && change !== undefined && (
            <div className="text-xs text-slate-500 font-medium">
              {trend === 'up' ? 'Increase' : 'Decrease'} from last period
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
