"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { orderService } from '@/services/orderService';
import { Order, OrderWithItems, CartItem, Transaction, OrderStatus, OrderItemStatus } from '@/types';
import { useEffect } from 'react';
import toast from 'react-hot-toast';

export const useOrders = () => {
  const queryClient = useQueryClient();

  const ordersQuery = useQuery({
    queryKey: ['orders'],
    queryFn: orderService.getOrders,
  });

  useEffect(() => {
    let channelPromise = orderService.subscribeToOrders((payload) => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      toast.success('New order received!');
    });

    return () => {
      channelPromise.then(channel => channel.unsubscribe());
    };
  }, [queryClient]);

  return ordersQuery;
};

export const useOrdersByStatus = (status: Order['status']) => {
  const queryClient = useQueryClient();

  const ordersQuery = useQuery({
    queryKey: ['orders', status],
    queryFn: () => orderService.getOrdersByStatus(status),
  });

  useEffect(() => {
    const channelPromise = orderService.subscribeToOrders((payload) => {
      queryClient.invalidateQueries({ queryKey: ['orders', status] });
      if (status === 'pending' || status === 'processing') {
        toast.success('New order received!');
      }
    });

    return () => {
      channelPromise.then(channel => channel.unsubscribe());
    };
  }, [queryClient, status]);

  return ordersQuery;
};

export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      orderData,
      cartItems,
      transactionData,
    }: {
      orderData: Omit<Order, 'id' | 'created_at' | 'updated_at'>;
      cartItems: CartItem[];
      transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'order_id'>;
    }) => orderService.createCompleteOrder(orderData, cartItems, transactionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      toast.success('Order created successfully!');
    },
    onError: (error) => {
      toast.error('Failed to create order');
      console.error('Error creating order:', error);
    },
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: OrderStatus }) =>
      orderService.updateOrderStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      toast.success('Order status updated!');
    },
    onError: (error) => {
      toast.error('Failed to update order status');
      console.error('Error updating order status:', error);
    },
  });
};

export const useUpdateOrderItemStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: OrderItemStatus }) =>
      orderService.updateOrderItemStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      toast.success('Item status updated!');
    },
    onError: (error) => {
      toast.error('Failed to update item status');
      console.error('Error updating item status:', error);
    },
  });
};
