"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { orderService } from '@/services/orderService';
import { Order, OrderWithItems, OrderStatus } from '@/types';
import toast from 'react-hot-toast';

export const useOrders = (filters?: {
  status?: string;
  orderType?: string;
  branchId?: string;
  startDate?: string;
  endDate?: string;
}) => {
  return useQuery({
    queryKey: ['orders', filters],
    queryFn: () => orderService.getOrders(filters),
  });
};

export const useOrdersByStatus = (status: OrderStatus) => {
  return useOrders({ status });
};

export const useOrderById = (id: string) => {
  return useQuery({
    queryKey: ['orders', id],
    queryFn: () => orderService.getOrderById(id),
    enabled: !!id,
  });
};

export const useKitchenOrders = (branchId?: string) => {
  return useQuery({
    queryKey: ['kitchen-orders', branchId],
    queryFn: () => orderService.getKitchenOrders(branchId),
    refetchInterval: 5000, // Refetch every 5 seconds for kitchen
  });
};

export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (orderData: {
      order_number: string;
      order_type: 'dine_in' | 'take_away' | 'delivery_grab' | 'delivery_gojek';
      table_number?: string;
      customer_name?: string;
      customer_phone?: string;
      subtotal: number;
      discount_amount?: number;
      tax_amount?: number;
      total_amount: number;
      notes?: string;
      cashier_id: string;
      branch_id: string;
      items: Array<{
        menu_item_id: string;
        quantity: number;
        unit_price: number;
        total_price: number;
        notes?: string;
        variant_selections?: any;
      }>;
    }) => orderService.createOrder(orderData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
      toast.success('Order created successfully!');
    },
    onError: (error) => {
      toast.error('Failed to create order');
      console.error('Error creating order:', error);
    },
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: OrderStatus }) =>
      orderService.updateOrderStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
      toast.success('Order status updated!');
    },
    onError: (error) => {
      toast.error('Failed to update order status');
      console.error('Error updating order status:', error);
    },
  });
};

export const useUpdateOrderItemPrepared = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, isPrepared }: { id: string; isPrepared: boolean }) =>
      orderService.updateOrderItemPrepared(id, isPrepared),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
      toast.success('Item status updated!');
    },
    onError: (error) => {
      toast.error('Failed to update item status');
      console.error('Error updating item status:', error);
    },
  });
};

export const useCreateTransaction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (transactionData: {
      order_id: string;
      payment_method: 'cash' | 'qris' | 'card' | 'split';
      amount_paid: number;
      change_amount?: number;
      payment_details?: any;
    }) => orderService.createTransaction(transactionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      toast.success('Payment processed successfully!');
    },
    onError: (error) => {
      toast.error('Failed to process payment');
      console.error('Error processing payment:', error);
    },
  });
};

export const useGenerateOrderNumber = () => {
  return useQuery({
    queryKey: ['order-number'],
    queryFn: orderService.generateOrderNumber,
    staleTime: 0, // Always generate fresh
    gcTime: 0, // Don't cache
  });
};
