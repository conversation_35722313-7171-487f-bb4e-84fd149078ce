"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Calendar } from 'lucide-react';

interface SalesComparisonProps {
  orders: any[];
  dateRange: { from: Date; to: Date };
}

export function SalesComparison({ orders, dateRange }: SalesComparisonProps) {
  // Group by category
  const categoryData = orders.reduce((acc, order) => {
    order.order_items?.forEach((item: any) => {
      const category = item.menu?.category || 'Other';
      if (!acc[category]) {
        acc[category] = { category, revenue: 0, quantity: 0 };
      }
      acc[category].revenue += item.price * item.quantity;
      acc[category].quantity += item.quantity;
    });
    return acc;
  }, {} as Record<string, any>);

  const chartData = Object.values(categoryData).sort((a: any, b: any) => b.revenue - a.revenue);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">Sales by Category</CardTitle>
        <Calendar className="h-4 w-4 text-lupizza-green-600" />
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-slate-200" />
              <XAxis 
                dataKey="category" 
                className="text-xs fill-slate-600"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs fill-slate-600"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip 
                formatter={(value: number) => [
                  new Intl.NumberFormat('id-ID', { 
                    style: 'currency', 
                    currency: 'IDR' 
                  }).format(value),
                  'Revenue'
                ]}
                labelStyle={{ color: '#334155' }}
                contentStyle={{ 
                  backgroundColor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar 
                dataKey="revenue" 
                fill="#22c55e"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}