'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Filter, X, SortAsc, SortDesc, Grid, List } from 'lucide-react';

interface Category {
  id: string;
  name: string;
  description?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface MenuFiltersProps {
  categories: Category[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  sortBy?: string;
  onSortChange?: (sort: string) => void;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
}

export function MenuFilters({
  categories,
  selectedCategory,
  onCategoryChange,
  sortBy = 'name',
  onSortChange,
  viewMode = 'grid',
  onViewModeChange,
}: MenuFiltersProps) {
  const clearFilters = () => {
    onCategoryChange('all');
  };

  const hasActiveFilters = selectedCategory !== 'all';

  const getSortLabel = (sort: string) => {
    switch (sort) {
      case 'name':
        return 'Name A-Z';
      case 'name_desc':
        return 'Name Z-A';
      case 'price':
        return 'Price Low-High';
      case 'price_desc':
        return 'Price High-Low';
      case 'category':
        return 'Category';
      case 'created_at':
        return 'Newest First';
      default:
        return 'Name A-Z';
    }
  };

  return (
    <div className="flex items-center justify-between gap-4">
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-slate-500" />
          <span className="text-sm font-medium text-slate-700">Filters:</span>
        </div>

        <Select value={selectedCategory} onValueChange={onCategoryChange}>
          <SelectTrigger className="w-40 bg-white border-slate-200">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent className="bg-white border border-slate-200 shadow-lg">
            <SelectItem value="all" className="hover:bg-slate-50">
              All Categories
            </SelectItem>
            {categories.map((category) => (
              <SelectItem
                key={category.id}
                value={category.name}
                className="hover:bg-slate-50"
              >
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {hasActiveFilters && (
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Category: {selectedCategory}</span>
              <button onClick={clearFilters} className="ml-1">
                <X className="h-3 w-3" />
              </button>
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-slate-600 hover:text-slate-900"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        {/* Sort Dropdown */}
        {onSortChange && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <SortAsc className="h-4 w-4" />
                {getSortLabel(sortBy)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-48 bg-white border border-slate-200 shadow-lg"
            >
              <DropdownMenuItem
                onClick={() => onSortChange('name')}
                className="hover:bg-slate-50"
              >
                <SortAsc className="h-4 w-4 mr-2" />
                Name A-Z
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSortChange('name_desc')}
                className="hover:bg-slate-50"
              >
                <SortDesc className="h-4 w-4 mr-2" />
                Name Z-A
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onSortChange('price')}
                className="hover:bg-slate-50"
              >
                <SortAsc className="h-4 w-4 mr-2" />
                Price Low-High
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSortChange('price_desc')}
                className="hover:bg-slate-50"
              >
                <SortDesc className="h-4 w-4 mr-2" />
                Price High-Low
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onSortChange('category')}
                className="hover:bg-slate-50"
              >
                Category
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onSortChange('created_at')}
                className="hover:bg-slate-50"
              >
                Newest First
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* View Mode Toggle */}
        {onViewModeChange && (
          <div className="flex border border-slate-200 rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="rounded-r-none border-r"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
