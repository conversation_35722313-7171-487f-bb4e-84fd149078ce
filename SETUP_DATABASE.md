# 🚀 Setup Database Lupizza POS

## ✅ Status Saat Ini
- ✅ Database connection berhasil
- ✅ Semua tabel sudah terbuat
- ❌ Sample data belum ada (perlu dijalankan migrations)
- ❌ User admin belum dibuat

## 📋 Langkah-langkah Selanjutnya

### 1. Jalankan Sample Data Migration

**Buka Supabase Dashboard:**
- Kunjungi: https://supabase.com/dashboard/project/ajujvjdvyqkhawuajvxn
- Login dengan akun Supabase Anda

**Jalankan Migration Sample Data:**
1. <PERSON><PERSON> "SQL Editor" di sidebar kiri
2. Klik "New query"
3. Copy seluruh isi file `src/supabase/migration/002_sample_data.sql`
4. Paste ke SQL Editor
5. Klik "Run" untuk mengeksekusi

**Verifikasi Sample Data:**
```bash
npm run test-db
```
Pastikan output menunjukkan:
- ✅ Categories: 5 records found
- ✅ Menu Items: 15+ records found
- ✅ Item Variants: 30+ records found

### 2. Setup RLS Policies

**Jalankan RLS Migration:**
1. <PERSON> SQL Editor, buat query baru
2. Copy seluruh isi file `src/supabase/migration/003_rls_policies.sql`
3. Paste ke SQL Editor
4. Klik "Run" untuk mengeksekusi

### 3. Buat Admin User

**Di Supabase Dashboard:**
1. Klik "Authentication" → "Users"
2. Klik "Add user"
3. Isi data:
   - Email: `<EMAIL>`
   - Password: `LupizzaAdmin2024!` (atau password yang kuat)
   - ✅ Auto Confirm User
4. Klik "Create user"
5. **COPY UUID USER** yang baru dibuat

**Insert User ke Tabel Users:**
1. Kembali ke SQL Editor
2. Jalankan query berikut (ganti UUID):
```sql
INSERT INTO users (id, email, full_name, role, branch_id) VALUES 
('PASTE_UUID_USER_DISINI', '<EMAIL>', 'Admin Lupizza', 'admin', '550e8400-e29b-41d4-a716-************');
```

### 4. Test Login

**Start Development Server:**
```bash
npm run dev
```

**Test Login:**
1. Buka: http://localhost:3000
2. Login dengan:
   - Email: `<EMAIL>`
   - Password: `LupizzaAdmin2024!`

### 5. Verifikasi Setup Lengkap

**Test Database:**
```bash
npm run test-db
```

**Test Realtime:**
```bash
npm run test-realtime
```

## 🚨 Troubleshooting

### Error "relation does not exist"
- Pastikan migration 001 sudah dijalankan
- Cek di Table Editor apakah tabel sudah ada

### Error "permission denied"
- Pastikan RLS policies sudah dijalankan (migration 003)
- Cek apakah user sudah dibuat di Authentication

### Login gagal
- Pastikan user sudah di-insert ke tabel `users`
- Cek apakah UUID user sudah benar
- Pastikan password sesuai

### Sample data kosong
- Jalankan migration 002 (sample data)
- Cek di Table Editor apakah data sudah masuk

## 📞 Bantuan

Jika masih ada masalah, jalankan:
```bash
npm run test-db
```

Dan berikan output error yang muncul untuk troubleshooting lebih lanjut.

---

**Setelah semua langkah selesai, Anda siap melanjutkan ke Fase 2: Struktur Kode & API Contract!** 🎉
