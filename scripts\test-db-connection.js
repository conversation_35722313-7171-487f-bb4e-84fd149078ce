// Test Database Connection Script
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Make sure .env.local contains:');
  console.log('NEXT_PUBLIC_SUPABASE_URL=your_url');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔄 Testing Supabase connection...\n');

  try {
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const { data, error } = await supabase.from('branches').select('count');
    if (error) throw error;
    console.log('✅ Basic connection successful\n');

    // Test 2: Check tables exist
    console.log('2. Checking if tables exist...');
    const tables = ['branches', 'categories', 'menu_items', 'item_variants', 'orders', 'order_items'];
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('count');
      if (error) {
        console.log(`❌ Table '${table}' not found or accessible`);
        throw error;
      }
      console.log(`✅ Table '${table}' exists`);
    }
    console.log('');

    // Test 3: Check sample data
    console.log('3. Checking sample data...');
    
    const { data: categories, error: catError } = await supabase
      .from('categories')
      .select('*');
    
    if (catError) throw catError;
    console.log(`✅ Categories: ${categories.length} records found`);
    
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*');
    
    if (menuError) throw menuError;
    console.log(`✅ Menu Items: ${menuItems.length} records found`);
    
    const { data: variants, error: varError } = await supabase
      .from('item_variants')
      .select('*');
    
    if (varError) throw varError;
    console.log(`✅ Item Variants: ${variants.length} records found\n`);

    // Test 4: Check RLS is enabled
    console.log('4. Checking RLS status...');
    const { data: rlsData, error: rlsError } = await supabase
      .rpc('check_rls_enabled');
    
    if (rlsError) {
      console.log('⚠️  Could not check RLS status (this is normal if function not created)');
    } else {
      console.log('✅ RLS check completed');
    }
    console.log('');

    // Test 5: Sample query with joins
    console.log('5. Testing complex query...');
    const { data: menuWithCategories, error: joinError } = await supabase
      .from('menu_items')
      .select(`
        id,
        name,
        base_price,
        categories (
          name
        )
      `)
      .limit(5);
    
    if (joinError) throw joinError;
    console.log(`✅ Complex query successful: ${menuWithCategories.length} items with categories\n`);

    console.log('🎉 All tests passed! Database is ready for use.');
    console.log('\nNext steps:');
    console.log('1. Create admin user in Supabase Auth dashboard');
    console.log('2. Insert user record in users table');
    console.log('3. Start development: npm run dev');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.log('\nTroubleshooting:');
    console.log('1. Make sure all migrations have been run in Supabase dashboard');
    console.log('2. Check if RLS policies are properly configured');
    console.log('3. Verify environment variables are correct');
    process.exit(1);
  }
}

testConnection();
