"use client";

import { useUser } from '@/hooks/useUser';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Navbar } from './Navbar';
import { Sidebar } from './Sidebar';
import { Loader2, Shield, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useQueryClient } from '@tanstack/react-query';

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiredRole: 'admin' | 'cashier' | 'kitchen';
}

export function DashboardLayout({ children, requiredRole }: DashboardLayoutProps) {
  const { data: user, isLoading, error, refetch } = useUser();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        setIsRedirecting(true);
        // Clear cache and redirect to login
        queryClient.clear();
        router.push('/login');
        return;
      }
      
      if (user.role !== requiredRole) {
        setIsRedirecting(true);
        // Redirect to appropriate dashboard based on user role
        switch (user.role) {
          case 'admin':
            router.push('/dashboard/admin');
            break;
          case 'cashier':
            router.push('/dashboard/cashier');
            break;
          case 'kitchen':
            router.push('/dashboard/kitchen');
            break;
          default:
            queryClient.clear();
            router.push('/login');
        }
      }
    }
  }, [user, isLoading, router, requiredRole, queryClient]);

  // Loading state
  if (isLoading || isRedirecting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lupizza-green-50 via-white to-lupizza-cream-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-lupizza-green-600 mx-auto mb-4" />
          <p className="text-slate-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lupizza-red-50 via-white to-lupizza-cream-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-lupizza-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="h-8 w-8 text-lupizza-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Authentication Error</h2>
            <p className="text-slate-600 mb-4">
              There was an error loading your session. Please try logging in again.
            </p>
            <div className="space-y-2">
              <Button 
                onClick={() => refetch()}
                variant="outline"
                className="w-full"
              >
                Retry
              </Button>
              <Button 
                onClick={() => {
                  queryClient.clear();
                  router.push('/login');
                }}
                className="w-full bg-lupizza-red-600 hover:bg-lupizza-red-700"
              >
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Unauthorized access
  if (!user || user.role !== requiredRole) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lupizza-red-50 via-white to-lupizza-cream-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-lupizza-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-lupizza-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Access Denied</h2>
            <p className="text-slate-600 mb-4">
              You don't have permission to access this page. Required role: <span className="font-semibold capitalize">{requiredRole}</span>
            </p>
            <div className="space-y-2">
              <Button 
                onClick={() => {
                  switch (user?.role) {
                    case 'admin':
                      router.push('/dashboard/admin');
                      break;
                    case 'cashier':
                      router.push('/dashboard/cashier');
                      break;
                    case 'kitchen':
                      router.push('/dashboard/kitchen');
                      break;
                    default:
                      queryClient.clear();
                      router.push('/login');
                  }
                }}
                className="w-full bg-lupizza-green-600 hover:bg-lupizza-green-700"
              >
                Go to My Dashboard
              </Button>
              <Button 
                variant="outline"
                onClick={() => {
                  queryClient.clear();
                  router.push('/login');
                }}
                className="w-full"
              >
                Logout & Login Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-lupizza-green-50/30">
      <Navbar />
      <div className="flex h-[calc(100vh-4rem)]">
        <Sidebar role={requiredRole} />
        <main className="flex-1 overflow-y-auto">
          <div className="p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
