'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';
import { Order, OrderItem, RealtimePayload } from '@/types';

// Hook for real-time orders (Kitchen Dashboard)
export function useRealtimeOrders(branchId?: string) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let channel: RealtimeChannel;

    const fetchInitialOrders = async () => {
      try {
        setLoading(true);
        let query = supabase
          .from('orders')
          .select(`
            *,
            order_items (
              *,
              menu_items (*)
            ),
            users (*),
            branches (*)
          `)
          .in('status', ['pending', 'processing'])
          .order('created_at', { ascending: true });

        if (branchId) {
          query = query.eq('branch_id', branchId);
        }

        const { data, error } = await query;

        if (error) throw error;
        setOrders(data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch orders');
      } finally {
        setLoading(false);
      }
    };

    const setupRealtimeSubscription = () => {
      channel = supabase
        .channel('orders-channel')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'orders',
            filter: branchId ? `branch_id=eq.${branchId}` : undefined,
          },
          (payload: RealtimePayload<Order>) => {
            console.log('Order change received:', payload);
            
            if (payload.eventType === 'INSERT') {
              // Fetch the complete order with relations
              fetchOrderWithRelations(payload.new.id).then((order) => {
                if (order) {
                  setOrders((prev) => [order, ...prev]);
                }
              });
            } else if (payload.eventType === 'UPDATE') {
              setOrders((prev) =>
                prev.map((order) =>
                  order.id === payload.new.id ? { ...order, ...payload.new } : order
                )
              );
            } else if (payload.eventType === 'DELETE') {
              setOrders((prev) => prev.filter((order) => order.id !== payload.old.id));
            }
          }
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'order_items',
          },
          (payload: RealtimePayload<OrderItem>) => {
            console.log('Order item change received:', payload);
            
            // Update the specific order item
            setOrders((prev) =>
              prev.map((order) => ({
                ...order,
                order_items: order.order_items?.map((item) =>
                  item.id === payload.new?.id ? { ...item, ...payload.new } : item
                ) || [],
              }))
            );
          }
        )
        .subscribe();
    };

    const fetchOrderWithRelations = async (orderId: string) => {
      try {
        const { data, error } = await supabase
          .from('orders')
          .select(`
            *,
            order_items (
              *,
              menu_items (*)
            ),
            users (*),
            branches (*)
          `)
          .eq('id', orderId)
          .single();

        if (error) throw error;
        return data;
      } catch (err) {
        console.error('Failed to fetch order with relations:', err);
        return null;
      }
    };

    fetchInitialOrders();
    setupRealtimeSubscription();

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [branchId]);

  return { orders, loading, error, setOrders };
}

// Hook for real-time order items (Kitchen Dashboard)
export function useRealtimeOrderItems(orderId: string) {
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let channel: RealtimeChannel;

    const fetchInitialOrderItems = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('order_items')
          .select(`
            *,
            menu_items (*)
          `)
          .eq('order_id', orderId)
          .order('created_at', { ascending: true });

        if (error) throw error;
        setOrderItems(data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch order items');
      } finally {
        setLoading(false);
      }
    };

    const setupRealtimeSubscription = () => {
      channel = supabase
        .channel(`order-items-${orderId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'order_items',
            filter: `order_id=eq.${orderId}`,
          },
          (payload: RealtimePayload<OrderItem>) => {
            console.log('Order item change received:', payload);
            
            if (payload.eventType === 'INSERT') {
              setOrderItems((prev) => [...prev, payload.new]);
            } else if (payload.eventType === 'UPDATE') {
              setOrderItems((prev) =>
                prev.map((item) =>
                  item.id === payload.new.id ? { ...item, ...payload.new } : item
                )
              );
            } else if (payload.eventType === 'DELETE') {
              setOrderItems((prev) => prev.filter((item) => item.id !== payload.old.id));
            }
          }
        )
        .subscribe();
    };

    if (orderId) {
      fetchInitialOrderItems();
      setupRealtimeSubscription();
    }

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [orderId]);

  return { orderItems, loading, error, setOrderItems };
}

// Hook for real-time notifications
export function useRealtimeNotifications(userId: string) {
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    let channel: RealtimeChannel;

    if (userId) {
      channel = supabase
        .channel(`notifications-${userId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'activity_logs',
            filter: `user_id=eq.${userId}`,
          },
          (payload) => {
            console.log('New notification:', payload);
            setNotifications((prev) => [payload.new, ...prev]);
          }
        )
        .subscribe();
    }

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [userId]);

  return { notifications, setNotifications };
}
