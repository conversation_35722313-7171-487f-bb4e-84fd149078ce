"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { menuService } from '@/services/menuService';
import { MenuItem, Category } from '@/types';
import toast from 'react-hot-toast';

export const useMenuItems = () => {
  return useQuery({
    queryKey: ['menu-items'],
    queryFn: menuService.getMenuItems,
  });
};

export const useMenuItemsByCategory = (categoryId: string) => {
  return useQuery({
    queryKey: ['menu-items', categoryId],
    queryFn: () => menuService.getMenuItemsByCategory(categoryId),
    enabled: !!categoryId,
  });
};

export const useMenuItemsWithVariants = () => {
  return useQuery({
    queryKey: ['menu-items-with-variants'],
    queryFn: menuService.getMenuItemsWithVariants,
  });
};

export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: menuService.getCategories,
  });
};

// Backward compatibility
export const useMenu = useMenuItems;
export const useMenusByCategory = useMenuItemsByCategory;

export const useCreateMenuItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (menuItem: {
      name: string;
      description?: string;
      category_id: string;
      base_price: number;
      price_grab?: number;
      price_gojek?: number;
      image_url?: string;
      is_available?: boolean;
      is_beverage?: boolean;
      sort_order?: number;
      image_file?: File | null;
    }) => menuService.createMenuItem(menuItem),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items-with-variants'] });
      toast.success('Menu item created successfully!');
    },
    onError: (error) => {
      toast.error('Failed to create menu item');
      console.error('Error creating menu item:', error);
    },
  });
};

export const useUpdateMenuItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: {
      id: string;
      updates: Partial<MenuItem> & { image_file?: File | null }
    }) => menuService.updateMenuItem(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items-with-variants'] });
      toast.success('Menu item updated successfully!');
    },
    onError: (error) => {
      toast.error('Failed to update menu item');
      console.error('Error updating menu item:', error);
    },
  });
};

export const useDeleteMenuItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => menuService.deleteMenuItem(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items-with-variants'] });
      toast.success('Menu item deleted successfully!');
    },
    onError: (error) => {
      toast.error('Failed to delete menu item');
      console.error('Error deleting menu item:', error);
    },
  });
};

export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (category: {
      name: string;
      description?: string;
      sort_order?: number;
      is_active?: boolean;
    }) => menuService.createCategory(category),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('Category created successfully!');
    },
    onError: (error) => {
      toast.error('Failed to create category');
      console.error('Error creating category:', error);
    },
  });
};

// Backward compatibility
export const useCreateMenu = useCreateMenuItem;
export const useUpdateMenu = useUpdateMenuItem;
export const useDeleteMenu = useDeleteMenuItem;