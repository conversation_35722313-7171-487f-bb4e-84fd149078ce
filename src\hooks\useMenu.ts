"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { menuService } from '@/services/menuService';
import { Menu } from '@/types';
import toast from 'react-hot-toast';

export const useMenu = () => {
  return useQuery({
    queryKey: ['menus'],
    queryFn: menuService.getMenus,
  });
};

export const useMenusByCategory = (category: string) => {
  return useQuery({
    queryKey: ['menus', category],
    queryFn: () => menuService.getMenusByCategory(category),
    enabled: !!category,
  });
};

export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: menuService.getCategories,
  });
};

export const useCreateMenu = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (menu: Omit<Menu, 'id' | 'created_at' | 'updated_at'> & { image_file?: File | null }) =>
      menuService.createMenu(menu),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menus'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('Menu item created successfully!');
    },
    onError: (error) => {
      toast.error('Failed to create menu item');
      console.error('Error creating menu:', error);
    },
  });
};

export const useUpdateMenu = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Menu> & { image_file?: File | null } }) =>
      menuService.updateMenu(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menus'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('Menu item updated successfully!');
    },
    onError: (error) => {
      toast.error('Failed to update menu item');
      console.error('Error updating menu:', error);
    },
  });
};

export const useDeleteMenu = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => menuService.deleteMenu(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menus'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('Menu item deleted successfully!');
    },
    onError: (error) => {
      toast.error('Failed to delete menu item');
      console.error('Error deleting menu:', error);
    },
  });
};