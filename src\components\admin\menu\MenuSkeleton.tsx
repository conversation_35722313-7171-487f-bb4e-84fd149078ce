import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface MenuSkeletonProps {
  viewMode?: 'grid' | 'list';
  count?: number;
}

export function MenuSkeleton({ viewMode = 'grid', count = 8 }: MenuSkeletonProps) {
  if (viewMode === 'list') {
    return (
      <div className="space-y-4">
        {Array.from({ length: count }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-center space-x-6">
                {/* Image skeleton */}
                <div className="w-24 h-24 bg-slate-200 rounded-lg flex-shrink-0"></div>
                
                {/* Content skeleton */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="h-6 bg-slate-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-slate-200 rounded w-full mb-1"></div>
                      <div className="h-4 bg-slate-200 rounded w-2/3 mb-3"></div>
                      <div className="flex space-x-2">
                        <div className="h-5 bg-slate-200 rounded w-16"></div>
                        <div className="h-5 bg-slate-200 rounded w-20"></div>
                      </div>
                    </div>
                    
                    {/* Price and actions skeleton */}
                    <div className="flex items-center space-x-4 ml-4">
                      <div className="h-8 bg-slate-200 rounded w-24"></div>
                      <div className="h-6 bg-slate-200 rounded w-12"></div>
                      <div className="flex space-x-2">
                        <div className="h-8 w-8 bg-slate-200 rounded"></div>
                        <div className="h-8 w-8 bg-slate-200 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Grid view skeleton
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          {/* Image skeleton */}
          <div className="aspect-video bg-slate-200 rounded-t-lg"></div>
          
          <CardHeader className="pb-2">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="h-5 bg-slate-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-slate-200 rounded w-16"></div>
              </div>
              <div className="h-5 bg-slate-200 rounded w-20"></div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="h-6 bg-slate-200 rounded w-24"></div>
              <div className="flex items-center space-x-2">
                <div className="h-5 bg-slate-200 rounded w-12"></div>
                <div className="h-4 w-4 bg-slate-200 rounded"></div>
              </div>
            </div>
            
            <div className="h-4 bg-slate-200 rounded w-full"></div>
            <div className="h-4 bg-slate-200 rounded w-2/3"></div>
            
            <div className="flex justify-between pt-2">
              <div className="h-8 bg-slate-200 rounded w-16"></div>
              <div className="h-8 bg-slate-200 rounded w-16"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
