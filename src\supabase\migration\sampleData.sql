/*
  # Sample Data for LuPizza POS

  1. Sample profiles (admin, cashier, kitchen)
  2. Sample menu items with different categories
  3. Sample orders with order items and transactions
  4. Sample activity logs

  Note: This is for development/testing purposes only
*/

-- Insert sample profiles
-- Note: These user IDs should match actual auth.users entries
-- You'll need to create these users through Supabase Auth first

-- Sample menu items
INSERT INTO menus (name, category, price, description, image_url, available) VALUES
-- Pizza
('Margherita Pizza', 'Pizza', 65000, 'Classic pizza with tomato sauce, mozzarella, and fresh basil', 'https://images.pexels.com/photos/1146760/pexels-photo-1146760.jpeg', true),
('Pepperoni Pizza', 'Pizza', 75000, 'Pizza with pepperoni, mozzarella, and tomato sauce', 'https://images.pexels.com/photos/1146760/pexels-photo-1146760.jpeg', true),
('Hawaiian Pizza', 'Pizza', 80000, 'Pizza with ham, pineapple, and mozzarella', 'https://images.pexels.com/photos/1146760/pexels-photo-1146760.jpeg', true),
('Meat Lovers Pizza', 'Pizza', 95000, 'Pizza with pepperoni, sausage, ham, and bacon', 'https://images.pexels.com/photos/1146760/pexels-photo-1146760.jpeg', true),
('Vegetarian Pizza', 'Pizza', 85000, 'Pizza with bell peppers, mushrooms, onions, and olives', 'https://images.pexels.com/photos/1146760/pexels-photo-1146760.jpeg', true),
('BBQ Chicken Pizza', 'Pizza', 90000, 'Pizza with BBQ sauce, chicken, onions, and cilantro', 'https://images.pexels.com/photos/1146760/pexels-photo-1146760.jpeg', true),

-- Beverages
('Coca Cola', 'Beverages', 15000, 'Classic Coca Cola', 'https://images.pexels.com/photos/50593/coca-cola-cold-drink-soft-drink-coke-50593.jpeg', true),
('Sprite', 'Beverages', 15000, 'Refreshing lemon-lime soda', 'https://images.pexels.com/photos/50593/coca-cola-cold-drink-soft-drink-coke-50593.jpeg', true),
('Orange Juice', 'Beverages', 20000, 'Fresh orange juice', 'https://images.pexels.com/photos/1346155/pexels-photo-1346155.jpeg', true),
('Iced Tea', 'Beverages', 12000, 'Cold brewed tea', 'https://images.pexels.com/photos/1346155/pexels-photo-1346155.jpeg', true),
('Coffee', 'Beverages', 18000, 'Hot brewed coffee', 'https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg', true),
('Mineral Water', 'Beverages', 8000, 'Bottled mineral water', 'https://images.pexels.com/photos/1346155/pexels-photo-1346155.jpeg', true),

-- Appetizers
('Garlic Bread', 'Appetizers', 25000, 'Toasted bread with garlic butter', 'https://images.pexels.com/photos/1893556/pexels-photo-1893556.jpeg', true),
('Chicken Wings', 'Appetizers', 45000, 'Crispy chicken wings with choice of sauce', 'https://images.pexels.com/photos/1893556/pexels-photo-1893556.jpeg', true),
('Mozzarella Sticks', 'Appetizers', 35000, 'Breaded mozzarella sticks with marinara sauce', 'https://images.pexels.com/photos/1893556/pexels-photo-1893556.jpeg', true),
('Onion Rings', 'Appetizers', 30000, 'Crispy battered onion rings', 'https://images.pexels.com/photos/1893556/pexels-photo-1893556.jpeg', true),
('Bruschetta', 'Appetizers', 40000, 'Toasted bread with tomatoes, basil, and garlic', 'https://images.pexels.com/photos/1893556/pexels-photo-1893556.jpeg', true),

-- Pasta
('Spaghetti Carbonara', 'Pasta', 55000, 'Creamy pasta with bacon and parmesan', 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg', true),
('Fettuccine Alfredo', 'Pasta', 50000, 'Creamy white sauce pasta', 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg', true),
('Penne Arrabbiata', 'Pasta', 48000, 'Spicy tomato sauce pasta', 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg', true),
('Lasagna', 'Pasta', 65000, 'Layered pasta with meat sauce and cheese', 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg', true),

-- Desserts
('Tiramisu', 'Desserts', 35000, 'Classic Italian dessert', 'https://images.pexels.com/photos/1099680/pexels-photo-1099680.jpeg', true),
('Chocolate Cake', 'Desserts', 30000, 'Rich chocolate cake slice', 'https://images.pexels.com/photos/1099680/pexels-photo-1099680.jpeg', true),
('Ice Cream', 'Desserts', 25000, 'Vanilla, chocolate, or strawberry', 'https://images.pexels.com/photos/1099680/pexels-photo-1099680.jpeg', true),
('Panna Cotta', 'Desserts', 32000, 'Creamy Italian dessert', 'https://images.pexels.com/photos/1099680/pexels-photo-1099680.jpeg', true);

-- You can add sample orders, order_items, and transactions here
-- But they would require actual profile IDs from auth.users table
-- This is better done through the application after user registration

-- Sample activity logs structure (would require real user IDs)
-- INSERT INTO activity_logs (user_id, action, description) VALUES
-- ('user-id-here', 'login', 'User logged in to the system'),
-- ('user-id-here', 'create_order', 'Created new order #12345'),
-- ('user-id-here', 'update_menu', 'Updated menu item: Margherita Pizza');