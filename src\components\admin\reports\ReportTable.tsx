"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { FileText, TrendingUp, Users, ChefHat } from 'lucide-react';

interface ReportTableProps {
  data: any[];
  reportType: 'sales' | 'menu' | 'staff';
  isLoading: boolean;
}

export function ReportTable({ data, reportType, isLoading }: ReportTableProps) {
  if (isLoading) {
    return (
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              {[...Array(5)].map((_, i) => (
                <TableHead key={i}>
                  <div className="h-4 bg-slate-200 rounded w-20 animate-pulse"></div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(10)].map((_, i) => (
              <TableRow key={i}>
                {[...Array(5)].map((_, j) => (
                  <TableCell key={j}>
                    <div className="h-4 bg-slate-200 rounded w-16 animate-pulse"></div>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  const renderSalesReport = () => (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-slate-50">
            <TableHead className="font-semibold">Order ID</TableHead>
            <TableHead className="font-semibold">Date</TableHead>
            <TableHead className="font-semibold">Type</TableHead>
            <TableHead className="font-semibold">Items</TableHead>
            <TableHead className="font-semibold">Total</TableHead>
            <TableHead className="font-semibold">Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((order) => (
            <TableRow key={order.id} className="hover:bg-slate-50">
              <TableCell className="font-mono text-sm">#{order.id.slice(-8)}</TableCell>
              <TableCell>{format(new Date(order.created_at), 'MMM dd, HH:mm')}</TableCell>
              <TableCell>
                <Badge variant="outline" className="capitalize">
                  {order.order_type}
                </Badge>
              </TableCell>
              <TableCell>{order.order_items?.length || 0} items</TableCell>
              <TableCell className="font-medium text-lupizza-green-600">
                {new Intl.NumberFormat('id-ID', { 
                  style: 'currency', 
                  currency: 'IDR' 
                }).format(order.total)}
              </TableCell>
              <TableCell>
                <Badge 
                  variant={order.status === 'completed' ? 'default' : 'secondary'}
                  className={order.status === 'completed' ? 'bg-green-100 text-green-800' : ''}
                >
                  {order.status}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  const renderMenuReport = () => (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-slate-50">
            <TableHead className="font-semibold">Menu Item</TableHead>
            <TableHead className="font-semibold">Category</TableHead>
            <TableHead className="font-semibold">Orders</TableHead>
            <TableHead className="font-semibold">Quantity Sold</TableHead>
            <TableHead className="font-semibold">Revenue</TableHead>
            <TableHead className="font-semibold">Avg. Price</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item, index) => (
            <TableRow key={index} className="hover:bg-slate-50">
              <TableCell className="font-medium">{item.name}</TableCell>
              <TableCell>
                <Badge variant="outline" className="capitalize">
                  {item.category}
                </Badge>
              </TableCell>
              <TableCell>{item.orderCount}</TableCell>
              <TableCell className="font-medium">{item.quantity}</TableCell>
              <TableCell className="font-medium text-lupizza-green-600">
                {new Intl.NumberFormat('id-ID', { 
                  style: 'currency', 
                  currency: 'IDR' 
                }).format(item.revenue)}
              </TableCell>
              <TableCell>
                {new Intl.NumberFormat('id-ID', { 
                  style: 'currency', 
                  currency: 'IDR' 
                }).format(item.avgPrice)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  const renderStaffReport = () => (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-slate-50">
            <TableHead className="font-semibold">Staff Member</TableHead>
            <TableHead className="font-semibold">Role</TableHead>
            <TableHead className="font-semibold">Orders Handled</TableHead>
            <TableHead className="font-semibold">Total Sales</TableHead>
            <TableHead className="font-semibold">Avg. Order Value</TableHead>
            <TableHead className="font-semibold">Performance</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((staff, index) => (
            <TableRow key={index} className="hover:bg-slate-50">
              <TableCell className="font-medium">{staff.name}</TableCell>
              <TableCell>
                <Badge 
                  variant="outline"
                  className={
                    staff.role === 'admin' ? 'bg-red-100 text-red-800' :
                    staff.role === 'cashier' ? 'bg-green-100 text-green-800' :
                    'bg-blue-100 text-blue-800'
                  }
                >
                  {staff.role}
                </Badge>
              </TableCell>
              <TableCell>{staff.ordersHandled}</TableCell>
              <TableCell className="font-medium text-lupizza-green-600">
                {new Intl.NumberFormat('id-ID', { 
                  style: 'currency', 
                  currency: 'IDR' 
                }).format(staff.totalSales)}
              </TableCell>
              <TableCell>
                {new Intl.NumberFormat('id-ID', { 
                  style: 'currency', 
                  currency: 'IDR' 
                }).format(staff.avgOrderValue)}
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span className="text-sm text-green-700">Excellent</span>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  if (data.length === 0) {
    const icons = {
      sales: TrendingUp,
      menu: ChefHat,
      staff: Users
    };
    const Icon = icons[reportType];

    return (
      <div className="text-center py-12 border rounded-lg">
        <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon className="h-12 w-12 text-slate-400" />
        </div>
        <h3 className="text-lg font-medium text-slate-900 mb-2">No data available</h3>
        <p className="text-slate-600">No {reportType} data found for the selected period.</p>
      </div>
    );
  }

  switch (reportType) {
    case 'sales':
      return renderSalesReport();
    case 'menu':
      return renderMenuReport();
    case 'staff':
      return renderStaffReport();
    default:
      return null;
  }
}